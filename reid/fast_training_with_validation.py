#!/usr/bin/env python3
"""
快速训练和验证 - 边训练边验证效果，避免白忙活
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
from pathlib import Path
import random
import time
import logging
from typing import Dict, List, Tuple
from PIL import Image
import torchvision.transforms as transforms
from collections import defaultdict
import timm

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from model_validator import validate_model

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FastTripletDataset(Dataset):
    """快速三元组数据集 - 优化版本"""
    
    def __init__(self, dataset_path: str, max_cats: int = 100):
        self.dataset_path = Path(dataset_path)
        self.max_cats = max_cats
        
        # 简化的数据增强
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.RandomHorizontalFlip(p=0.5),
            transforms.RandomRotation(degrees=15),
            transforms.ColorJitter(brightness=0.3, contrast=0.3, saturation=0.3, hue=0.1),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        # 构建数据集
        self.cat_images = defaultdict(list)
        self.cat_ids = []
        self._build_dataset()
        
        logger.info(f"快速数据集构建完成: {len(self.cat_ids)} 只猫咪")
    
    def _build_dataset(self):
        """构建数据集"""
        cat_folders = [f for f in self.dataset_path.iterdir() 
                      if f.is_dir() and f.name.isdigit()]
        
        valid_cats = []
        for cat_folder in cat_folders:
            images = []
            for ext in ['*.jpg', '*.JPG', '*.jpeg', '*.JPEG', '*.png', '*.PNG']:
                images.extend(list(cat_folder.glob(ext)))
            
            if len(images) >= 5:
                valid_cats.append((cat_folder.name, images))
        
        # 按图片数量排序
        valid_cats.sort(key=lambda x: len(x[1]), reverse=True)
        selected_cats = valid_cats[:self.max_cats]
        
        for cat_id, images in selected_cats:
            self.cat_images[cat_id] = [str(img) for img in images]
            self.cat_ids.append(cat_id)
        
        logger.info(f"选择了 {len(selected_cats)} 只猫咪，总图片: {sum(len(imgs) for imgs in self.cat_images.values())}")
    
    def __len__(self):
        return len(self.cat_ids) * 100  # 恢复到合理的样本数
    
    def __getitem__(self, idx):
        """生成三元组"""
        anchor_cat = random.choice(self.cat_ids)
        
        # 选择anchor和positive
        anchor_img = random.choice(self.cat_images[anchor_cat])
        positive_img = random.choice(self.cat_images[anchor_cat])
        while positive_img == anchor_img and len(self.cat_images[anchor_cat]) > 1:
            positive_img = random.choice(self.cat_images[anchor_cat])
        
        # 选择negative
        negative_cat = random.choice([c for c in self.cat_ids if c != anchor_cat])
        negative_img = random.choice(self.cat_images[negative_cat])
        
        try:
            anchor = self.transform(Image.open(anchor_img).convert('RGB'))
            positive = self.transform(Image.open(positive_img).convert('RGB'))
            negative = self.transform(Image.open(negative_img).convert('RGB'))
            
            return anchor, positive, negative
        except Exception as e:
            return self.__getitem__(random.randint(0, len(self) - 1))

class FastMegaDescriptor(nn.Module):
    """快速MegaDescriptor - 1024维"""
    
    def __init__(self, feature_dim=1024):
        super().__init__()
        
        # MegaDescriptor骨干网络
        self.backbone = timm.create_model(
            'hf-hub:BVRA/MegaDescriptor-T-224',
            pretrained=True,
            num_classes=0
        )
        
        # 获取骨干网络输出维度
        with torch.no_grad():
            dummy_input = torch.randn(1, 3, 224, 224)
            backbone_output = self.backbone(dummy_input)
            backbone_dim = backbone_output.shape[1]
        
        # 简化的特征增强网络
        self.feature_enhancer = nn.Sequential(
            nn.Linear(backbone_dim, feature_dim * 2),
            nn.BatchNorm1d(feature_dim * 2),
            nn.GELU(),
            nn.Dropout(0.2),
            
            nn.Linear(feature_dim * 2, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.GELU(),
            nn.Dropout(0.1),
            
            nn.Linear(feature_dim, feature_dim)
        )
        
        logger.info(f"快速MegaDescriptor初始化: {backbone_dim} -> {feature_dim}")
    
    def forward(self, x):
        backbone_features = self.backbone(x)
        enhanced_features = self.feature_enhancer(backbone_features)
        return torch.nn.functional.normalize(enhanced_features, p=2, dim=1)

class FastTrainer:
    """快速训练器 - 边训练边验证"""
    
    def __init__(self, dataset_path: str, max_cats: int = 100):
        self.dataset_path = dataset_path
        self.max_cats = max_cats
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # 数据集和数据加载器 - 使用与成功训练相同的配置
        self.dataset = FastTripletDataset(dataset_path, max_cats)
        self.dataloader = DataLoader(
            self.dataset,
            batch_size=12,  # 与成功的150猫训练相同
            shuffle=True,
            num_workers=6,  # 与成功的150猫训练相同
            pin_memory=True,
            persistent_workers=True
        )
        
        # 模型
        self.model = FastMegaDescriptor(feature_dim=1024).to(self.device)
        
        # 损失函数
        self.criterion = nn.TripletMarginLoss(margin=0.5)
        
        # 优化器
        self.optimizer = optim.AdamW([
            {'params': self.model.backbone.parameters(), 'lr': 1e-5},
            {'params': self.model.feature_enhancer.parameters(), 'lr': 1e-3}
        ], weight_decay=1e-4)
        
        # 学习率调度
        self.scheduler = optim.lr_scheduler.OneCycleLR(
            self.optimizer,
            max_lr=[1e-5, 1e-3],
            epochs=5,  # 只训练5轮
            steps_per_epoch=len(self.dataloader),
            pct_start=0.2
        )
        
        logger.info(f"快速训练器初始化完成: {max_cats} 只猫咪, {len(self.dataloader)} 批次/轮")
    
    def train_epoch(self, epoch: int):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        for batch_idx, (anchor, positive, negative) in enumerate(self.dataloader):
            anchor = anchor.to(self.device)
            positive = positive.to(self.device)
            negative = negative.to(self.device)
            
            # 前向传播
            anchor_feat = self.model(anchor)
            positive_feat = self.model(positive)
            negative_feat = self.model(negative)
            
            # 计算损失
            loss = self.criterion(anchor_feat, positive_feat, negative_feat)
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            self.optimizer.step()
            self.scheduler.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            if batch_idx % 20 == 0:
                lr = self.scheduler.get_last_lr()[1]
                logger.info(f"Epoch {epoch}, Batch {batch_idx}: Loss={loss.item():.4f}, LR={lr:.2e}")
        
        return total_loss / num_batches if num_batches > 0 else 0.0
    
    def evaluate_separability(self):
        """评估特征分离度"""
        self.model.eval()
        
        eval_cats = random.sample(self.dataset.cat_ids, min(30, len(self.dataset.cat_ids)))
        intra_dists = []
        inter_dists = []
        
        with torch.no_grad():
            for cat_id in eval_cats:
                images = self.dataset.cat_images[cat_id]
                
                # 类内距离
                if len(images) >= 2:
                    img1, img2 = random.sample(images, 2)
                    
                    tensor1 = self.dataset.transform(Image.open(img1).convert('RGB')).unsqueeze(0).to(self.device)
                    tensor2 = self.dataset.transform(Image.open(img2).convert('RGB')).unsqueeze(0).to(self.device)
                    
                    feat1 = self.model(tensor1)
                    feat2 = self.model(tensor2)
                    
                    intra_dist = torch.norm(feat1 - feat2, p=2).item()
                    intra_dists.append(intra_dist)
                
                # 类间距离
                other_cat = random.choice([c for c in eval_cats if c != cat_id])
                other_img = random.choice(self.dataset.cat_images[other_cat])
                
                tensor1 = self.dataset.transform(Image.open(random.choice(images)).convert('RGB')).unsqueeze(0).to(self.device)
                tensor2 = self.dataset.transform(Image.open(other_img).convert('RGB')).unsqueeze(0).to(self.device)
                
                feat1 = self.model(tensor1)
                feat2 = self.model(tensor2)
                
                inter_dist = torch.norm(feat1 - feat2, p=2).item()
                inter_dists.append(inter_dist)
        
        avg_intra = np.mean(intra_dists) if intra_dists else 0.0
        avg_inter = np.mean(inter_dists) if inter_dists else 0.0
        separability = avg_inter / (avg_intra + 1e-8)
        
        return {
            'intra_distance': avg_intra,
            'inter_distance': avg_inter,
            'separability': separability
        }
    
    def validate_on_real_task(self, save_path: str):
        """在真实任务上验证效果"""
        logger.info("🔍 开始真实任务验证...")

        # 保存当前模型
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'feature_dim': 1024,
            'model_type': 'FastMegaDescriptor'
        }, save_path)

        # 使用新的验证器
        result = validate_model(
            model_path=save_path,
            dataset_path=self.dataset_path,
            num_cats=min(10, len(self.dataset.cat_ids)),
            strategy='balanced'
        )

        accuracy = result['accuracy']
        logger.info(f"✅ 真实任务验证结果: {accuracy:.1%} ({result['correct']}/{result['total']})")

        return accuracy
    
    def train_with_validation(self, epochs: int = 5, save_path: str = 'fast_megadescriptor.pth'):
        """边训练边验证"""
        logger.info(f"🚀 开始快速训练: {epochs} epochs, {self.max_cats} 只猫咪")
        
        best_accuracy = 0.0
        training_history = []
        
        for epoch in range(epochs):
            start_time = time.time()
            
            # 训练
            avg_loss = self.train_epoch(epoch + 1)
            
            # 评估分离度
            eval_stats = self.evaluate_separability()
            
            # 真实任务验证
            accuracy = self.validate_on_real_task(save_path)
            
            epoch_time = time.time() - start_time
            
            # 记录历史
            training_history.append({
                'epoch': epoch + 1,
                'loss': avg_loss,
                'separability': eval_stats['separability'],
                'accuracy': accuracy,
                'time': epoch_time
            })
            
            logger.info(f"📊 Epoch {epoch + 1}/{epochs}:")
            logger.info(f"   损失: {avg_loss:.4f}")
            logger.info(f"   分离度: {eval_stats['separability']:.4f}")
            logger.info(f"   真实准确率: {accuracy:.1%}")
            logger.info(f"   用时: {epoch_time:.1f}s")
            
            # 保存最佳模型
            if accuracy > best_accuracy:
                best_accuracy = accuracy
                torch.save({
                    'model_state_dict': self.model.state_dict(),
                    'feature_dim': 1024,
                    'model_type': 'FastMegaDescriptor',
                    'accuracy': accuracy,
                    'separability': eval_stats['separability']
                }, save_path.replace('.pth', '_best.pth'))
                logger.info(f"🎉 保存最佳模型: 准确率 {best_accuracy:.1%}")
            
            # 早停检查
            if accuracy >= 0.95:
                logger.info(f"🎯 达到95%目标，提前停止训练!")
                break
        
        logger.info(f"🏁 快速训练完成! 最佳准确率: {best_accuracy:.1%}")
        
        return training_history, best_accuracy

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='快速训练和验证')
    parser.add_argument('--dataset', type=str, 
                       default='/home/<USER>/animsi/caby_training/dataset/cat_individual_images',
                       help='数据集路径')
    parser.add_argument('--max-cats', type=int, default=100,
                       help='最大猫咪数量')
    parser.add_argument('--epochs', type=int, default=5,
                       help='训练轮数')
    parser.add_argument('--output', type=str, default='fast_megadescriptor_100cats.pth',
                       help='模型保存路径')
    
    args = parser.parse_args()
    
    # 创建训练器
    trainer = FastTrainer(
        dataset_path=args.dataset,
        max_cats=args.max_cats
    )
    
    # 开始训练
    history, best_acc = trainer.train_with_validation(epochs=args.epochs, save_path=args.output)
    
    logger.info(f"🎉 快速训练完成! 最佳准确率: {best_acc:.1%}")

if __name__ == "__main__":
    main()
