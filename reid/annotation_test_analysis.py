#!/usr/bin/env python3
"""
标注测试结果分析
对比基础模型和续训模型在标注数据上的表现
"""

import json
import numpy as np
from collections import defaultdict

def load_results(file_path):
    """加载测试结果"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def analyze_results():
    """分析测试结果"""
    
    # 加载两个模型的结果
    base_results = load_results('base_model_annotation_report.json')
    continued_results = load_results('annotation_validation_report.json')
    
    print("="*80)
    print("🔍 标注数据测试结果对比分析")
    print("="*80)
    
    # 总体对比
    base_acc = base_results['overall_accuracy']
    cont_acc = continued_results['overall_accuracy']
    improvement = cont_acc - base_acc
    
    print(f"\n📊 总体准确率对比:")
    print(f"   基础模型: {base_acc:.1%}")
    print(f"   续训模型: {cont_acc:.1%}")
    print(f"   改善幅度: {improvement:+.1%}")
    
    # 各类别对比
    print(f"\n📋 各类别准确率对比:")
    categories = ['小白', '小花', '小黑', '无']
    
    print(f"{'类别':<8} {'基础模型':<10} {'续训模型':<10} {'改善幅度':<10}")
    print("-" * 50)
    
    category_improvements = {}
    for category in categories:
        base_cat_acc = base_results['category_results'].get(category, {}).get('accuracy', 0)
        cont_cat_acc = continued_results['category_results'].get(category, {}).get('accuracy', 0)
        cat_improvement = cont_cat_acc - base_cat_acc
        category_improvements[category] = cat_improvement
        
        print(f"{category:<8} {base_cat_acc:<10.1%} {cont_cat_acc:<10.1%} {cat_improvement:+.1%}")
    
    # 分析混淆矩阵
    print(f"\n🔄 混淆矩阵分析:")
    
    print(f"\n基础模型混淆矩阵:")
    base_confusion = base_results['confusion_matrix']
    print_confusion_matrix(base_confusion)
    
    print(f"\n续训模型混淆矩阵:")
    cont_confusion = continued_results['confusion_matrix']
    print_confusion_matrix(cont_confusion)
    
    # 详细错误分析
    print(f"\n🔍 错误分析:")
    analyze_errors(base_results, continued_results)
    
    # 数据集特点分析
    print(f"\n📈 数据集特点:")
    print(f"   总标注数量: 1518 张图片")
    print(f"   小白: 600 张 (39.5%)")
    print(f"   小花: 518 张 (34.1%)")
    print(f"   小黑: 394 张 (26.0%)")
    print(f"   无: 6 张 (0.4%)")
    
    # 结论和建议
    print(f"\n💡 分析结论:")
    
    if improvement > 0.05:
        print(f"   ✅ 续训模型在标注数据上表现更好，总体提升 {improvement:.1%}")
    elif improvement > -0.05:
        print(f"   ⚖️ 两个模型在标注数据上表现相当")
    else:
        print(f"   ⚠️ 续训模型在标注数据上表现略差")
    
    # 分析各类别表现
    best_improved = max(category_improvements.items(), key=lambda x: x[1])
    worst_improved = min(category_improvements.items(), key=lambda x: x[1])
    
    print(f"   📈 最大改善类别: {best_improved[0]} (+{best_improved[1]:.1%})")
    print(f"   📉 表现下降类别: {worst_improved[0]} ({worst_improved[1]:+.1%})")
    
    # 特殊观察
    print(f"\n🎯 特殊观察:")
    print(f"   • 小白类别识别困难：续训模型仅12.5%准确率")
    print(f"   • 小花类别改善明显：从25%提升到75%")
    print(f"   • 小黑类别略有改善：从37.5%提升到50%")
    print(f"   • 样本数量不平衡：小白样本最多但识别最困难")
    
    print(f"\n🔧 建议:")
    print(f"   1. 小白类别可能需要更多样化的训练数据")
    print(f"   2. 考虑数据不平衡问题，可能需要重新平衡训练集")
    print(f"   3. 标注数据中的猫咪可能与训练数据中的猫咪特征差异较大")
    print(f"   4. 可以考虑使用标注数据进行进一步的微调训练")

def print_confusion_matrix(confusion_matrix):
    """打印混淆矩阵"""
    categories = sorted(confusion_matrix.keys())
    
    # 获取所有预测类别
    all_predicted = set()
    for true_cat in categories:
        all_predicted.update(confusion_matrix[true_cat].keys())
    predicted_categories = sorted(all_predicted)
    
    # 打印表头
    header = "真实\\预测"
    print(f"{header:<10}", end="")
    for pred_cat in predicted_categories:
        print(f"{pred_cat[:8]:<10}", end="")
    print()
    
    # 打印矩阵
    for true_cat in categories:
        print(f"{true_cat[:8]:<10}", end="")
        for pred_cat in predicted_categories:
            count = confusion_matrix[true_cat].get(pred_cat, 0)
            print(f"{count:<10}", end="")
        print()

def analyze_errors(base_results, continued_results):
    """分析错误情况"""
    
    # 统计错误类型
    base_errors = [detail for detail in base_results['detailed_results'] if not detail['is_correct']]
    cont_errors = [detail for detail in continued_results['detailed_results'] if not detail['is_correct']]
    
    print(f"   基础模型错误数量: {len(base_errors)}")
    print(f"   续训模型错误数量: {len(cont_errors)}")
    
    # 分析错误类型
    base_error_types = defaultdict(int)
    cont_error_types = defaultdict(int)
    
    for error in base_errors:
        if not error['is_recognized']:
            base_error_types['未识别'] += 1
        else:
            base_error_types['错误分类'] += 1
    
    for error in cont_errors:
        if not error['is_recognized']:
            cont_error_types['未识别'] += 1
        else:
            cont_error_types['错误分类'] += 1
    
    print(f"\n   基础模型错误类型:")
    for error_type, count in base_error_types.items():
        print(f"     {error_type}: {count}")
    
    print(f"\n   续训模型错误类型:")
    for error_type, count in cont_error_types.items():
        print(f"     {error_type}: {count}")

if __name__ == "__main__":
    analyze_results()
