#!/usr/bin/env python3
"""
测试基于特征提取的猫咪识别系统
验证在原始三只猫咪上的100%识别能力和泛化能力
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
from pathlib import Path
import random
import logging
import json
from typing import Dict, List, Tuple, Optional
from PIL import Image
import torchvision.transforms as transforms
from collections import defaultdict
import timm

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FeatureExtractorModel(nn.Module):
    """特征提取模型"""
    
    def __init__(self, feature_dim: int = 2048):
        super().__init__()
        
        # 骨干网络
        self.backbone = timm.create_model(
            'hf-hub:BVRA/MegaDescriptor-T-224',
            pretrained=True,
            num_classes=0
        )
        
        # 获取骨干网络输出维度
        with torch.no_grad():
            dummy_input = torch.randn(1, 3, 224, 224)
            backbone_output = self.backbone(dummy_input)
            backbone_dim = backbone_output.shape[1]
        
        # 多层特征增强网络
        self.feature_enhancer = nn.Sequential(
            # 第一层：扩展特征
            nn.Linear(backbone_dim, feature_dim * 3),
            nn.BatchNorm1d(feature_dim * 3),
            nn.ReLU(),
            nn.Dropout(0.2),
            
            # 第二层：特征精炼
            nn.Linear(feature_dim * 3, feature_dim * 2),
            nn.BatchNorm1d(feature_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.15),
            
            # 第三层：特征压缩
            nn.Linear(feature_dim * 2, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            
            # 输出层：最终特征
            nn.Linear(feature_dim, feature_dim)
        )
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(
            embed_dim=feature_dim,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )
        
        self.feature_dim = feature_dim
    
    def forward(self, x):
        # 骨干网络特征提取
        backbone_features = self.backbone(x)
        
        # 特征增强
        enhanced_features = self.feature_enhancer(backbone_features)
        
        # 注意力机制（自注意力）
        enhanced_features_reshaped = enhanced_features.unsqueeze(1)  # [B, 1, D]
        attended_features, _ = self.attention(
            enhanced_features_reshaped, 
            enhanced_features_reshaped, 
            enhanced_features_reshaped
        )
        attended_features = attended_features.squeeze(1)  # [B, D]
        
        # 残差连接
        final_features = enhanced_features + attended_features
        
        # L2归一化
        normalized_features = torch.nn.functional.normalize(final_features, p=2, dim=1)
        
        return normalized_features

class FeatureTester:
    """特征提取器测试器"""
    
    def __init__(self, model_path: str, annotations_file: str, images_dir: str):
        self.model_path = model_path
        self.annotations_file = annotations_file
        self.images_dir = images_dir
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # 图像变换
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        # 加载模型
        self.model = self._load_model()
        
        # 加载标注数据
        with open(annotations_file, 'r', encoding='utf-8') as f:
            self.annotations = json.load(f)
        
        logger.info("特征提取器测试器初始化完成")
    
    def _load_model(self):
        """加载模型"""
        checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
        
        # 创建模型
        feature_dim = checkpoint.get('feature_dim', 2048)
        model = FeatureExtractorModel(feature_dim=feature_dim)
        
        # 加载权重
        model.load_state_dict(checkpoint['model_state_dict'])
        model = model.to(self.device)
        model.eval()
        
        logger.info(f"成功加载特征提取模型: 特征维度 {feature_dim}")
        return model
    
    def extract_features(self, image_path: str, verbose: bool = False) -> np.ndarray:
        """提取图像特征"""
        try:
            image = Image.open(image_path).convert('RGB')
            image_tensor = self.transform(image).unsqueeze(0).to(self.device)

            with torch.no_grad():
                features = self.model(image_tensor)
                features_np = features.cpu().numpy().flatten()

                if verbose:
                    print(f"\n🔍 特征提取详情 - {os.path.basename(image_path)}:")
                    print(f"   特征维度: {features_np.shape}")
                    print(f"   特征范围: [{features_np.min():.4f}, {features_np.max():.4f}]")
                    print(f"   特征均值: {features_np.mean():.4f}")
                    print(f"   特征标准差: {features_np.std():.4f}")
                    print(f"   L2范数: {np.linalg.norm(features_np):.4f}")
                    print(f"   前10个特征值: {features_np[:10]}")
                    print(f"   后10个特征值: {features_np[-10:]}")

                return features_np
        except Exception as e:
            logger.error(f"特征提取失败 {image_path}: {e}")
            return None
    
    def test_original_cats_100_percent(self, register_samples: int = 8, test_samples: int = 20) -> Dict:
        """测试原始三只猫咪的100%识别能力"""
        logger.info("开始测试原始三只猫咪的100%识别能力")
        
        # 过滤出三只主要猫咪
        valid_categories = ['小白', '小花', '小黑']
        category_images = defaultdict(list)
        
        for img_name, annotation in self.annotations.items():
            category = annotation.get('category', 'unknown')
            if category in valid_categories:
                img_path = os.path.join(self.images_dir, img_name)
                if os.path.exists(img_path):
                    category_images[category].append(img_path)
        
        # 为每个类别构建特征库
        category_features = {}
        category_test_images = {}
        
        for category in valid_categories:
            images = category_images[category]
            random.shuffle(images)
            
            # 注册图片
            register_images = images[:register_samples]
            test_images = images[register_samples:register_samples+test_samples]
            
            # 提取注册特征
            features_list = []
            for img_path in register_images:
                features = self.extract_features(img_path)
                if features is not None:
                    features_list.append(features)
            
            if features_list:
                category_features[category] = np.array(features_list)
                category_test_images[category] = test_images
        
        logger.info(f"特征库构建完成: {len(category_features)} 个类别")
        for category, features in category_features.items():
            logger.info(f"  {category}: {len(features)} 个注册特征, {len(category_test_images[category])} 张测试图片")
        
        # 测试识别
        results = {
            'total_tested': 0,
            'correct_predictions': 0,
            'category_results': {},
            'detailed_results': [],
            'confusion_matrix': defaultdict(lambda: defaultdict(int))
        }
        
        for true_category in valid_categories:
            test_images = category_test_images[true_category]
            category_correct = 0
            category_total = 0
            category_details = []
            
            for img_path in test_images:
                # 提取查询特征
                query_features = self.extract_features(img_path)
                if query_features is None:
                    continue
                
                # 与所有类别的特征库比较
                best_similarity = -1.0
                best_category = None
                similarities_with_categories = {}
                
                for ref_category, ref_features_list in category_features.items():
                    max_sim_with_category = -1.0
                    for ref_features in ref_features_list:
                        similarity = np.dot(query_features, ref_features)
                        max_sim_with_category = max(max_sim_with_category, similarity)
                    
                    similarities_with_categories[ref_category] = max_sim_with_category
                    
                    if max_sim_with_category > best_similarity:
                        best_similarity = max_sim_with_category
                        best_category = ref_category
                
                category_total += 1
                results['total_tested'] += 1
                
                # 判断是否正确
                is_correct = best_category == true_category
                
                if is_correct:
                    category_correct += 1
                    results['correct_predictions'] += 1
                
                # 更新混淆矩阵
                results['confusion_matrix'][true_category][best_category] += 1
                
                # 记录详细结果
                detail = {
                    'image_path': img_path,
                    'true_category': true_category,
                    'predicted_category': best_category,
                    'best_similarity': best_similarity,
                    'similarities': similarities_with_categories,
                    'is_correct': is_correct
                }
                category_details.append(detail)
                results['detailed_results'].append(detail)
            
            # 计算类别准确率
            category_accuracy = category_correct / category_total if category_total > 0 else 0.0
            results['category_results'][true_category] = {
                'accuracy': category_accuracy,
                'correct': category_correct,
                'total': category_total,
                'details': category_details
            }
            
            logger.info(f"{true_category}: {category_accuracy:.1%} ({category_correct}/{category_total})")
        
        # 计算总体准确率
        overall_accuracy = results['correct_predictions'] / results['total_tested'] if results['total_tested'] > 0 else 0.0
        results['overall_accuracy'] = overall_accuracy
        
        logger.info(f"原始三只猫咪测试完成: 总体准确率 {overall_accuracy:.1%}")
        
        return results
    
    def test_generalization_capability(self, num_new_cats: int = 10) -> Dict:
        """测试泛化能力（模拟新猫咪注册和识别）"""
        logger.info(f"开始测试泛化能力: 模拟 {num_new_cats} 只新猫咪")
        
        # 从cat_individual_images中选择一些猫咪作为"新猫咪"
        cat_individual_dir = "../dataset/cat_individual_images"
        if not os.path.exists(cat_individual_dir):
            logger.warning("cat_individual_images目录不存在，跳过泛化测试")
            return {}
        
        # 获取可用的猫咪ID
        available_cats = [d for d in os.listdir(cat_individual_dir) 
                         if os.path.isdir(os.path.join(cat_individual_dir, d))]
        
        if len(available_cats) < num_new_cats:
            logger.warning(f"可用猫咪数量不足: {len(available_cats)} < {num_new_cats}")
            num_new_cats = len(available_cats)
        
        # 随机选择新猫咪
        selected_cats = random.sample(available_cats, num_new_cats)
        
        # 为每只新猫咪构建特征库
        new_cat_features = {}
        new_cat_test_images = {}
        
        for cat_id in selected_cats:
            cat_dir = os.path.join(cat_individual_dir, cat_id)
            image_files = [f for f in os.listdir(cat_dir) 
                          if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
            
            if len(image_files) < 8:  # 需要足够的图片
                continue
            
            random.shuffle(image_files)
            
            # 注册图片（前5张）
            register_images = image_files[:5]
            test_images = image_files[5:10]  # 测试图片（接下来5张）
            
            # 提取注册特征
            features_list = []
            for img_file in register_images:
                img_path = os.path.join(cat_dir, img_file)
                features = self.extract_features(img_path)
                if features is not None:
                    features_list.append(features)
            
            if len(features_list) >= 3:  # 至少需要3个有效特征
                new_cat_features[cat_id] = np.array(features_list)
                new_cat_test_images[cat_id] = [os.path.join(cat_dir, f) for f in test_images]
        
        logger.info(f"新猫咪特征库构建完成: {len(new_cat_features)} 只猫咪")
        
        # 测试识别
        results = {
            'total_tested': 0,
            'correct_predictions': 0,
            'category_results': {},
            'overall_accuracy': 0.0
        }
        
        for cat_id in new_cat_features.keys():
            test_images = new_cat_test_images[cat_id]
            category_correct = 0
            category_total = 0
            
            for img_path in test_images:
                # 提取查询特征
                query_features = self.extract_features(img_path)
                if query_features is None:
                    continue
                
                # 与所有新猫咪的特征库比较
                best_similarity = -1.0
                best_cat_id = None
                
                for ref_cat_id, ref_features_list in new_cat_features.items():
                    max_sim_with_cat = -1.0
                    for ref_features in ref_features_list:
                        similarity = np.dot(query_features, ref_features)
                        max_sim_with_cat = max(max_sim_with_cat, similarity)
                    
                    if max_sim_with_cat > best_similarity:
                        best_similarity = max_sim_with_cat
                        best_cat_id = ref_cat_id
                
                category_total += 1
                results['total_tested'] += 1
                
                # 判断是否正确
                if best_cat_id == cat_id:
                    category_correct += 1
                    results['correct_predictions'] += 1
            
            # 计算类别准确率
            category_accuracy = category_correct / category_total if category_total > 0 else 0.0
            results['category_results'][cat_id] = {
                'accuracy': category_accuracy,
                'correct': category_correct,
                'total': category_total
            }
            
            logger.info(f"新猫咪 {cat_id}: {category_accuracy:.1%} ({category_correct}/{category_total})")
        
        # 计算总体准确率
        overall_accuracy = results['correct_predictions'] / results['total_tested'] if results['total_tested'] > 0 else 0.0
        results['overall_accuracy'] = overall_accuracy
        
        logger.info(f"泛化能力测试完成: 总体准确率 {overall_accuracy:.1%}")
        
        return results
    
    def comprehensive_test(self) -> Dict:
        """综合测试"""
        logger.info("开始综合测试")
        
        # 测试原始三只猫咪
        original_results = self.test_original_cats_100_percent()
        
        # 测试泛化能力
        generalization_results = self.test_generalization_capability()
        
        # 综合结果
        comprehensive_results = {
            'original_cats_test': original_results,
            'generalization_test': generalization_results,
            'summary': {
                'original_cats_accuracy': original_results.get('overall_accuracy', 0.0),
                'generalization_accuracy': generalization_results.get('overall_accuracy', 0.0),
                'meets_100_percent_target': original_results.get('overall_accuracy', 0.0) >= 1.0,
                'meets_95_percent_generalization': generalization_results.get('overall_accuracy', 0.0) >= 0.95
            }
        }
        
        return comprehensive_results

    def demonstrate_feature_output(self, num_samples: int = 3) -> None:
        """演示模型特征输出"""
        print("\n" + "="*80)
        print("🔍 模型特征输出演示")
        print("="*80)

        # 从每个类别选择一些图片进行演示
        valid_categories = ['小白', '小花', '小黑']
        category_images = defaultdict(list)

        for img_name, annotation in self.annotations.items():
            category = annotation.get('category', 'unknown')
            if category in valid_categories:
                img_path = os.path.join(self.images_dir, img_name)
                if os.path.exists(img_path):
                    category_images[category].append(img_path)

        # 为每个类别提取特征并展示
        category_features = {}

        for category in valid_categories:
            print(f"\n📂 {category} 类别特征:")
            print("-" * 50)

            images = category_images[category][:num_samples]
            features_list = []

            for i, img_path in enumerate(images):
                print(f"\n图片 {i+1}: {os.path.basename(img_path)}")
                features = self.extract_features(img_path, verbose=True)
                if features is not None:
                    features_list.append(features)

            if features_list:
                category_features[category] = features_list

        # 演示相似度计算
        print(f"\n" + "="*80)
        print("🔢 相似度计算演示")
        print("="*80)

        if len(category_features) >= 2:
            # 选择两个类别进行对比
            cat1, cat2 = list(category_features.keys())[:2]

            print(f"\n📊 {cat1} vs {cat2} 相似度对比:")
            print("-" * 50)

            # 同类别内部相似度
            if len(category_features[cat1]) >= 2:
                feat1_1 = category_features[cat1][0]
                feat1_2 = category_features[cat1][1]
                intra_similarity = np.dot(feat1_1, feat1_2)

                print(f"✅ {cat1} 内部相似度 (图片1 vs 图片2): {intra_similarity:.4f}")

                # 显示特征差异
                feat_diff = feat1_1 - feat1_2
                print(f"   特征差异统计:")
                print(f"     差异均值: {feat_diff.mean():.6f}")
                print(f"     差异标准差: {feat_diff.std():.6f}")
                print(f"     最大差异: {feat_diff.max():.6f}")
                print(f"     最小差异: {feat_diff.min():.6f}")

            # 不同类别间相似度
            if category_features[cat1] and category_features[cat2]:
                feat1 = category_features[cat1][0]
                feat2 = category_features[cat2][0]
                inter_similarity = np.dot(feat1, feat2)

                print(f"❌ {cat1} vs {cat2} 相似度: {inter_similarity:.4f}")

                # 显示特征差异
                feat_diff = feat1 - feat2
                print(f"   特征差异统计:")
                print(f"     差异均值: {feat_diff.mean():.6f}")
                print(f"     差异标准差: {feat_diff.std():.6f}")
                print(f"     最大差异: {feat_diff.max():.6f}")
                print(f"     最小差异: {feat_diff.min():.6f}")

                # 计算分离度
                if len(category_features[cat1]) >= 2:
                    separability = intra_similarity - inter_similarity
                    print(f"\n🎯 分离度 (内部相似度 - 外部相似度): {separability:.4f}")
                    if separability > 0.1:
                        print("   ✅ 分离度良好，模型能够区分不同类别")
                    elif separability > 0:
                        print("   ⚠️ 分离度一般，可能存在混淆")
                    else:
                        print("   ❌ 分离度不足，模型难以区分类别")

        # 特征分布分析
        print(f"\n" + "="*80)
        print("📈 特征分布分析")
        print("="*80)

        all_features = []
        for category, features_list in category_features.items():
            all_features.extend(features_list)

        if all_features:
            all_features = np.array(all_features)

            print(f"\n📊 整体特征统计:")
            print(f"   特征矩阵形状: {all_features.shape}")
            print(f"   特征均值: {all_features.mean():.6f}")
            print(f"   特征标准差: {all_features.std():.6f}")
            print(f"   特征最小值: {all_features.min():.6f}")
            print(f"   特征最大值: {all_features.max():.6f}")

            # 计算特征的方差
            feature_variances = np.var(all_features, axis=0)
            print(f"\n📊 特征维度分析:")
            print(f"   高方差维度数量 (>0.01): {np.sum(feature_variances > 0.01)}")
            print(f"   中方差维度数量 (0.001-0.01): {np.sum((feature_variances > 0.001) & (feature_variances <= 0.01))}")
            print(f"   低方差维度数量 (<0.001): {np.sum(feature_variances <= 0.001)}")
            print(f"   最高方差: {feature_variances.max():.6f}")
            print(f"   最低方差: {feature_variances.min():.6f}")

            # 显示最重要的特征维度
            top_variance_indices = np.argsort(feature_variances)[-10:]
            print(f"\n🔝 方差最高的10个特征维度:")
            for i, idx in enumerate(reversed(top_variance_indices)):
                print(f"   维度 {idx}: 方差 {feature_variances[idx]:.6f}")

        print(f"\n" + "="*80)
        print("💡 特征输出总结")
        print("="*80)
        print("1. 模型输出2048维的L2归一化特征向量")
        print("2. 特征值范围通常在[-1, 1]之间，L2范数≈1.0")
        print("3. 相似度通过余弦相似度计算（点积）")
        print("4. 好的特征应该有：同类高相似度，异类低相似度")
        print("5. 分离度 = 内部相似度 - 外部相似度，越大越好")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='测试特征提取器')
    parser.add_argument('--model', type=str, default='feature_extractor_model.pth', 
                       help='模型文件路径')
    parser.add_argument('--annotations', type=str, 
                       default='/home/<USER>/animsi/caby_training/tagging/annotations.json',
                       help='标注文件路径')
    parser.add_argument('--images', type=str, 
                       default='/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails',
                       help='图片目录路径')
    parser.add_argument('--output', type=str, default='feature_test_results.json', help='输出文件')
    
    args = parser.parse_args()
    
    # 检查文件存在性
    if not os.path.exists(args.model):
        print(f"❌ 模型文件不存在: {args.model}")
        return
    
    if not os.path.exists(args.annotations):
        print(f"❌ 标注文件不存在: {args.annotations}")
        return
    
    if not os.path.exists(args.images):
        print(f"❌ 图片目录不存在: {args.images}")
        return
    
    # 创建测试器
    tester = FeatureTester(args.model, args.annotations, args.images)
    
    # 执行综合测试
    results = tester.comprehensive_test()
    
    # 保存结果
    with open(args.output, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False, default=str)
    
    # 显示总结
    print("\n" + "="*60)
    print("🔍 基于特征提取的猫咪识别系统测试结果")
    print("="*60)
    
    summary = results['summary']
    
    print(f"\n📊 原始三只猫咪测试:")
    print(f"   准确率: {summary['original_cats_accuracy']:.1%}")
    print(f"   达到100%目标: {'✅' if summary['meets_100_percent_target'] else '❌'}")
    
    print(f"\n📊 泛化能力测试:")
    print(f"   准确率: {summary['generalization_accuracy']:.1%}")
    print(f"   达到95%目标: {'✅' if summary['meets_95_percent_generalization'] else '❌'}")
    
    print(f"\n🎯 总体评价:")
    if summary['meets_100_percent_target'] and summary['meets_95_percent_generalization']:
        print(f"   🏆 完美达成目标！既实现了100%原始识别，又达到了95%+泛化能力")
    elif summary['meets_100_percent_target']:
        print(f"   ✅ 原始识别目标达成，泛化能力需要进一步提升")
    else:
        print(f"   ⚠️ 需要继续优化以达到目标性能")
    
    print(f"\n📁 详细结果已保存: {args.output}")

if __name__ == "__main__":
    main()
