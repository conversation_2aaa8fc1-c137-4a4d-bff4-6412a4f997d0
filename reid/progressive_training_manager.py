#!/usr/bin/env python3
"""
渐进式续训策略管理器
协调整个续训过程：困难样本识别→置信度校准→稳定性优化
"""

import os
import sys
import time
import logging
import json
from typing import Dict, List, Tuple
import subprocess

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProgressiveTrainingManager:
    """渐进式训练管理器"""
    
    def __init__(self, base_model_path: str, split_file: str = 'dataset_split.json'):
        self.base_model_path = base_model_path
        self.split_file = split_file
        self.training_stages = []
        self.current_stage = 0
        
        # 训练阶段配置
        self.stage_configs = [
            {
                'name': '困难样本分析',
                'description': '分析当前模型的困难样本和过度自信问题',
                'action': 'analyze',
                'params': {
                    'cats': 50,
                    'samples': 10
                }
            },
            {
                'name': '第一阶段续训',
                'description': '针对困难样本进行初步续训，重点改善特征分离度',
                'action': 'train',
                'params': {
                    'epochs': 8,
                    'focus': 'separability'
                }
            },
            {
                'name': '中期评估',
                'description': '评估第一阶段续训效果，调整后续策略',
                'action': 'evaluate',
                'params': {
                    'cats': 30,
                    'samples': 8
                }
            },
            {
                'name': '第二阶段续训',
                'description': '基于评估结果进行精细化续训，优化置信度校准',
                'action': 'train',
                'params': {
                    'epochs': 10,
                    'focus': 'confidence'
                }
            },
            {
                'name': '最终验证',
                'description': '全面验证续训效果，确保性能提升',
                'action': 'validate',
                'params': {
                    'cats': 100,
                    'rounds': 5
                }
            }
        ]
        
        logger.info(f"渐进式训练管理器初始化完成: {len(self.stage_configs)} 个阶段")
    
    def run_analysis_stage(self, params: Dict) -> Dict:
        """运行困难样本分析阶段"""
        logger.info("🔍 开始困难样本分析阶段")
        
        analysis_file = 'corner_case_analysis.json'
        
        # 运行分析
        cmd = [
            'python', 'corner_case_analyzer.py',
            '--model', self.base_model_path,
            '--split-file', self.split_file,
            '--cats', str(params['cats']),
            '--samples', str(params['samples']),
            '--output', analysis_file
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            if result.returncode != 0:
                logger.error(f"分析失败: {result.stderr}")
                logger.error(f"分析输出: {result.stdout}")
                return {'success': False, 'error': result.stderr}

            # 显示分析输出
            if result.stdout:
                logger.info("分析输出:")
                for line in result.stdout.split('\n'):
                    if line.strip():
                        logger.info(f"  {line}")
            
            # 读取分析结果
            with open(analysis_file, 'r') as f:
                analysis_data = json.load(f)
            
            # 提取关键指标
            stats = analysis_data.get('overall_stats', {})
            corner_cases = len(analysis_data.get('corner_cases', []))
            confidence_issues = len(analysis_data.get('confidence_issues', []))
            
            result_summary = {
                'success': True,
                'analysis_file': analysis_file,
                'avg_separability': stats.get('avg_separability', 0.0),
                'poor_separability_count': stats.get('poor_separability_count', 0),
                'corner_cases': corner_cases,
                'confidence_issues': confidence_issues
            }
            
            separability = result_summary['avg_separability']
            if isinstance(separability, (int, float)):
                sep_str = f"{separability:.3f}"
            else:
                sep_str = str(separability)
            logger.info(f"✅ 分析完成: 分离度={sep_str}, "
                       f"困难样本={corner_cases}, 置信度问题={confidence_issues}")
            
            return result_summary
            
        except subprocess.TimeoutExpired:
            logger.error("分析超时")
            return {'success': False, 'error': 'timeout'}
        except Exception as e:
            logger.error(f"分析异常: {e}")
            return {'success': False, 'error': str(e)}
    
    def run_training_stage(self, stage_name: str, params: Dict, analysis_file: str = 'corner_case_analysis.json') -> Dict:
        """运行训练阶段"""
        logger.info(f"🚀 开始{stage_name}")
        
        # 根据阶段调整参数
        if params['focus'] == 'separability':
            output_model = 'hard_sample_stage1_model.pth'
            epochs = params['epochs']
        else:  # confidence
            output_model = 'hard_sample_stage2_model.pth'
            epochs = params['epochs']
            # 使用第一阶段的模型作为基础
            if os.path.exists('hard_sample_stage1_model.pth'):
                base_model = 'hard_sample_stage1_model.pth'
            else:
                base_model = self.base_model_path
        
        base_model = getattr(self, 'current_model', self.base_model_path)
        
        # 运行续训
        cmd = [
            'python', 'hard_sample_continued_training.py',
            '--base-model', base_model,
            '--analysis', analysis_file,
            '--split-file', self.split_file,
            '--epochs', str(epochs),
            '--output', output_model
        ]
        
        try:
            start_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)  # 1小时超时
            training_time = time.time() - start_time
            
            if result.returncode != 0:
                logger.error(f"训练失败: {result.stderr}")
                return {'success': False, 'error': result.stderr}
            
            # 更新当前模型
            self.current_model = output_model
            
            # 读取训练历史
            history_file = output_model.replace('.pth', '_history.json')
            training_history = []
            if os.path.exists(history_file):
                with open(history_file, 'r') as f:
                    training_history = json.load(f)
            
            # 提取最终性能
            final_performance = {}
            if training_history:
                last_epoch = training_history[-1]
                final_performance = {
                    'final_loss': last_epoch.get('avg_loss', 0.0),
                    'final_separability': last_epoch.get('eval_stats', {}).get('avg_separability', 0.0),
                    'final_intra_similarity': last_epoch.get('eval_stats', {}).get('avg_intra_similarity', 0.0),
                    'final_inter_similarity': last_epoch.get('eval_stats', {}).get('avg_max_inter_similarity', 0.0)
                }
            
            result_summary = {
                'success': True,
                'model_path': output_model,
                'training_time': training_time,
                'epochs_completed': len(training_history),
                'performance': final_performance
            }
            
            final_sep = final_performance.get('final_separability', 0.0)
            if isinstance(final_sep, (int, float)):
                final_sep_str = f"{final_sep:.3f}"
            else:
                final_sep_str = str(final_sep)
            logger.info(f"✅ {stage_name}完成: 用时={training_time:.1f}s, "
                       f"最终分离度={final_sep_str}")
            
            return result_summary
            
        except subprocess.TimeoutExpired:
            logger.error("训练超时")
            return {'success': False, 'error': 'timeout'}
        except Exception as e:
            logger.error(f"训练异常: {e}")
            return {'success': False, 'error': str(e)}
    
    def run_evaluation_stage(self, params: Dict) -> Dict:
        """运行评估阶段"""
        logger.info("📊 开始中期评估阶段")
        
        current_model = getattr(self, 'current_model', self.base_model_path)
        
        # 重新分析当前模型
        analysis_result = self.run_analysis_stage(params)
        
        if not analysis_result['success']:
            return analysis_result
        
        # 评估改善情况
        improvement_assessment = self._assess_improvement(analysis_result)
        
        result_summary = {
            'success': True,
            'current_model': current_model,
            'analysis_result': analysis_result,
            'improvement': improvement_assessment
        }
        
        logger.info(f"✅ 中期评估完成: 改善程度={improvement_assessment['level']}")
        
        return result_summary
    
    def run_validation_stage(self, params: Dict) -> Dict:
        """运行最终验证阶段"""
        logger.info("🎯 开始最终验证阶段")
        
        current_model = getattr(self, 'current_model', self.base_model_path)
        
        # 运行全面测试
        cmd = [
            'python', 'quick_test_v2.py',
            '--model', current_model,
            '--strategy', 'adaptive',
            '--cats', str(params['cats'])
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)
            if result.returncode != 0:
                logger.error(f"验证失败: {result.stderr}")
                return {'success': False, 'error': result.stderr}
            
            # 解析输出获取准确率
            output_lines = result.stdout.split('\n')
            accuracy = 0.0
            confidence = 0.0
            
            for line in output_lines:
                if '准确率:' in line:
                    try:
                        accuracy = float(line.split('准确率:')[1].split('%')[0].strip()) / 100
                    except:
                        pass
                elif '置信度:' in line:
                    try:
                        confidence = float(line.split('置信度:')[1].split('%')[0].strip()) / 100
                    except:
                        pass
            
            result_summary = {
                'success': True,
                'model_path': current_model,
                'accuracy': accuracy,
                'confidence': confidence,
                'meets_target': accuracy >= 0.95  # 95%目标
            }
            
            logger.info(f"✅ 最终验证完成: 准确率={accuracy:.1%}, 置信度={confidence:.1%}")
            
            return result_summary
            
        except subprocess.TimeoutExpired:
            logger.error("验证超时")
            return {'success': False, 'error': 'timeout'}
        except Exception as e:
            logger.error(f"验证异常: {e}")
            return {'success': False, 'error': str(e)}
    
    def _assess_improvement(self, current_analysis: Dict) -> Dict:
        """评估改善程度"""
        separability = current_analysis.get('avg_separability', 0.0)
        corner_cases = current_analysis.get('corner_cases', 0)
        confidence_issues = current_analysis.get('confidence_issues', 0)
        
        # 改善程度评估
        if separability > 0.2:
            level = 'excellent'
        elif separability > 0.1:
            level = 'good'
        elif separability > 0.0:
            level = 'moderate'
        elif separability > -0.1:
            level = 'slight'
        else:
            level = 'poor'
        
        return {
            'level': level,
            'separability': separability,
            'corner_cases': corner_cases,
            'confidence_issues': confidence_issues,
            'recommendation': self._get_recommendation(level, separability)
        }
    
    def _get_recommendation(self, level: str, separability: float) -> str:
        """获取改进建议"""
        if level == 'excellent':
            return "模型性能优秀，可以进行最终验证"
        elif level == 'good':
            return "模型性能良好，建议进行精细化续训"
        elif level == 'moderate':
            return "模型有所改善，需要继续优化"
        elif level == 'slight':
            return "改善轻微，建议调整训练策略"
        else:
            return "改善不明显，需要重新评估训练方法"
    
    def run_progressive_training(self) -> Dict:
        """运行完整的渐进式训练"""
        logger.info("🚀 开始渐进式续训流程")
        
        overall_results = {
            'start_time': time.time(),
            'stages': [],
            'success': True,
            'final_model': None
        }
        
        analysis_file = 'corner_case_analysis.json'
        
        for i, stage_config in enumerate(self.stage_configs):
            stage_name = stage_config['name']
            stage_action = stage_config['action']
            stage_params = stage_config['params']
            
            logger.info(f"\n{'='*60}")
            logger.info(f"阶段 {i+1}/{len(self.stage_configs)}: {stage_name}")
            logger.info(f"描述: {stage_config['description']}")
            logger.info(f"{'='*60}")
            
            stage_start_time = time.time()
            
            # 根据动作类型执行相应操作
            if stage_action == 'analyze':
                stage_result = self.run_analysis_stage(stage_params)
                if stage_result['success']:
                    analysis_file = stage_result['analysis_file']
            elif stage_action == 'train':
                stage_result = self.run_training_stage(stage_name, stage_params, analysis_file)
            elif stage_action == 'evaluate':
                stage_result = self.run_evaluation_stage(stage_params)
                # 根据评估结果调整后续训练参数
                if stage_result['success']:
                    improvement = stage_result['improvement']
                    if improvement['level'] in ['poor', 'slight']:
                        # 调整第二阶段参数
                        self.stage_configs[3]['params']['epochs'] = 15  # 增加训练轮数
            elif stage_action == 'validate':
                stage_result = self.run_validation_stage(stage_params)
            else:
                stage_result = {'success': False, 'error': f'未知动作: {stage_action}'}
            
            stage_time = time.time() - stage_start_time
            
            # 记录阶段结果
            stage_summary = {
                'stage': i + 1,
                'name': stage_name,
                'action': stage_action,
                'time': stage_time,
                'result': stage_result
            }
            overall_results['stages'].append(stage_summary)
            
            # 检查阶段是否成功
            if not stage_result.get('success', False):
                logger.error(f"❌ 阶段 {stage_name} 失败: {stage_result.get('error', '未知错误')}")
                overall_results['success'] = False
                break
            
            logger.info(f"✅ 阶段 {stage_name} 完成，用时 {stage_time:.1f}s")
        
        overall_results['total_time'] = time.time() - overall_results['start_time']
        overall_results['final_model'] = getattr(self, 'current_model', self.base_model_path)
        
        # 生成最终报告
        self._generate_final_report(overall_results)
        
        return overall_results
    
    def _generate_final_report(self, results: Dict):
        """生成最终报告"""
        logger.info(f"\n{'='*60}")
        logger.info("🎉 渐进式续训完成 - 最终报告")
        logger.info(f"{'='*60}")
        
        logger.info(f"总用时: {results['total_time']:.1f}s ({results['total_time']/60:.1f}分钟)")
        logger.info(f"总体成功: {'✅' if results['success'] else '❌'}")
        logger.info(f"最终模型: {results['final_model']}")
        
        logger.info(f"\n📊 各阶段总结:")
        for stage in results['stages']:
            status = "✅" if stage['result'].get('success', False) else "❌"
            logger.info(f"  {status} {stage['name']}: {stage['time']:.1f}s")
        
        # 保存详细报告
        report_path = 'progressive_training_report.json'
        with open(report_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"\n📁 详细报告已保存: {report_path}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='渐进式续训管理器')
    parser.add_argument('--base-model', type=str, required=True, help='基础模型路径')
    parser.add_argument('--split-file', type=str, default='dataset_split.json', help='数据集分割文件')
    
    args = parser.parse_args()
    
    # 检查基础模型
    if not os.path.exists(args.base_model):
        print(f"❌ 基础模型文件不存在: {args.base_model}")
        return
    
    # 创建管理器
    manager = ProgressiveTrainingManager(args.base_model, args.split_file)
    
    # 运行渐进式训练
    results = manager.run_progressive_training()
    
    if results['success']:
        logger.info("🎉 渐进式续训全部完成!")
    else:
        logger.error("❌ 渐进式续训未完全成功，请查看报告")

if __name__ == "__main__":
    main()
