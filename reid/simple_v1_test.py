#!/usr/bin/env python3
"""
简单V1模型测试 - 修复加载问题
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
from pathlib import Path
import random
import time
import logging
import json
from typing import Dict, List, Tuple
from PIL import Image
import torchvision.transforms as transforms
import timm

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedMegaDescriptor(nn.Module):
    """增强的MegaDescriptor - V1版本架构"""
    
    def __init__(self, feature_dim=1024):
        super().__init__()
        
        # MegaDescriptor骨干网络
        self.backbone = timm.create_model(
            'hf-hub:BVRA/MegaDescriptor-T-224',
            pretrained=True,
            num_classes=0
        )
        
        # 获取骨干网络输出维度
        with torch.no_grad():
            dummy_input = torch.randn(1, 3, 224, 224)
            backbone_output = self.backbone(dummy_input)
            backbone_dim = backbone_output.shape[1]
        
        # 特征增强网络 - V1架构
        self.feature_enhancer = nn.Sequential(
            nn.Linear(backbone_dim, feature_dim * 2),  # 768 -> 2048
            nn.BatchNorm1d(feature_dim * 2),
            nn.ReLU(inplace=True),
            nn.Dropout(0.2),
            nn.Linear(feature_dim * 2, feature_dim),   # 2048 -> 1024
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(0.1),
            nn.Linear(feature_dim, feature_dim)        # 1024 -> 1024
        )
        
        logger.info(f"V1 MegaDescriptor初始化: {backbone_dim} -> {feature_dim}维")
    
    def forward(self, x):
        # 骨干网络特征
        backbone_features = self.backbone(x)
        
        # 特征增强
        enhanced_features = self.feature_enhancer(backbone_features)
        
        # L2归一化
        normalized_features = torch.nn.functional.normalize(enhanced_features, p=2, dim=1)
        
        return normalized_features

def load_v1_model(model_path: str, device='cpu'):
    """加载V1模型"""
    try:
        # 修复weights_only问题
        checkpoint = torch.load(model_path, map_location=device, weights_only=False)
        
        # 获取特征维度
        feature_dim = checkpoint.get('feature_dim', 1024)
        
        # 创建模型
        model = EnhancedMegaDescriptor(feature_dim=feature_dim).to(device)
        
        # 加载权重
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        logger.info(f"成功加载V1模型: 特征维度 {feature_dim}")
        return model, feature_dim
        
    except Exception as e:
        logger.error(f"加载V1模型失败: {e}")
        raise

def extract_features(model, image_path: str, device='cpu'):
    """提取特征"""
    try:
        # 图像预处理
        transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        image = Image.open(image_path).convert('RGB')
        image_tensor = transform(image).unsqueeze(0).to(device)
        
        with torch.no_grad():
            features = model(image_tensor)
            return features.cpu().numpy().flatten()
            
    except Exception as e:
        logger.error(f"特征提取失败 {image_path}: {e}")
        return np.random.randn(1024).astype(np.float32)

def simple_v1_test(model_path: str, num_cats: int = 15):
    """简单V1测试"""
    
    logger.info(f"🧪 简单V1测试: {model_path}")
    
    # 加载模型
    model, feature_dim = load_v1_model(model_path, device='cpu')
    
    # 加载数据集分割信息
    with open('dataset_split.json', 'r') as f:
        split_info = json.load(f)
    
    test_cats = split_info['test_data']
    test_cat_ids = [cat['cat_id'] for cat in test_cats]
    
    # 构建测试集图片字典
    test_cat_images = {}
    for cat in test_cats:
        test_cat_images[cat['cat_id']] = cat['images']
    
    # 随机选择测试猫咪
    if len(test_cat_ids) < num_cats:
        selected_cats = test_cat_ids
    else:
        selected_cats = random.sample(test_cat_ids, num_cats)
    
    # 构建特征数据库
    feature_database = {}
    
    logger.info("构建特征数据库...")
    for cat_id in selected_cats:
        if cat_id not in test_cat_images:
            continue
        
        images = test_cat_images[cat_id]
        if len(images) < 5:
            continue
        
        # 分割注册和测试图片
        train_count = max(3, int(len(images) * 0.7))
        train_images = images[:train_count]
        
        # 提取训练特征
        cat_features = []
        for img_path in train_images:
            features = extract_features(model, img_path, device='cpu')
            cat_features.append(features)
        
        if len(cat_features) >= 3:
            feature_database[cat_id] = cat_features
    
    if len(feature_database) < 3:
        logger.warning(f"注册成功的猫咪数量不足: {len(feature_database)}")
        return None
    
    logger.info(f"特征数据库构建完成: {len(feature_database)} 只猫咪")
    
    # 识别测试
    correct = 0
    total = 0
    similarities = []
    
    logger.info("开始识别测试...")
    for cat_id in feature_database.keys():
        images = test_cat_images[cat_id]
        train_count = max(3, int(len(images) * 0.7))
        test_images = images[train_count:]
        
        if not test_images:
            continue
        
        # 随机选择一张测试图片
        test_image = random.choice(test_images)
        query_features = extract_features(model, test_image, device='cpu')
        
        # 计算与所有注册猫咪的相似度
        best_similarity = -1.0
        best_match_id = None
        
        for db_cat_id, db_features_list in feature_database.items():
            cat_similarities = []
            for db_features in db_features_list:
                # 余弦相似度
                similarity = np.dot(query_features, db_features)
                cat_similarities.append(similarity)
            
            # 使用最高相似度
            max_similarity = max(cat_similarities)
            
            if max_similarity > best_similarity:
                best_similarity = max_similarity
                best_match_id = db_cat_id
        
        total += 1
        is_correct = best_match_id == cat_id
        
        if is_correct:
            correct += 1
        
        similarities.append(best_similarity)
        
        # 显示结果
        status = "✅" if is_correct else "❌"
        logger.info(f"{status} 真实:{cat_id} 预测:{best_match_id} 相似度:{best_similarity:.3f}")
    
    # 计算统计
    accuracy = correct / total if total > 0 else 0.0
    avg_similarity = np.mean(similarities) if similarities else 0.0
    
    print(f"\n🎉 V1模型简单测试结果:")
    print(f"📊 准确率: {accuracy:.1%} ({correct}/{total})")
    print(f"📊 平均相似度: {avg_similarity:.3f}")
    print(f"📊 特征维度: {feature_dim}")
    print(f"📊 测试规模: {len(feature_database)} 只猫咪")
    
    return {
        'accuracy': accuracy,
        'avg_similarity': avg_similarity,
        'feature_dim': feature_dim,
        'total_cats': len(feature_database),
        'correct': correct,
        'total': total
    }

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='简单V1测试')
    parser.add_argument('--model', type=str, default='fast_megadescriptor_100cats_best.pth',
                       help='模型路径')
    parser.add_argument('--cats', type=int, default=15,
                       help='测试猫咪数量')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.model):
        print(f"❌ 模型文件不存在: {args.model}")
        return
    
    try:
        result = simple_v1_test(args.model, args.cats)
        
        if result:
            print(f"\n🎯 测试完成! 准确率: {result['accuracy']:.1%}")
        else:
            print(f"\n❌ 测试失败")
            
    except Exception as e:
        logger.error(f"测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
