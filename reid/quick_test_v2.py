#!/usr/bin/env python3
"""
快速测试脚本 V2 - 测试优化模型
"""

import os
import sys
import random
import logging
import json
from optimized_recognizer_v2 import validate_optimized_model

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def quick_test_all_models():
    """快速测试所有可用模型"""
    
    # 检查可用模型
    models = []
    
    # 检查现有的成功模型
    if os.path.exists('fast_megadescriptor_100cats_best.pth'):
        models.append(('fast_megadescriptor_100cats_best.pth', '1024维最佳模型'))
    
    if os.path.exists('fast_megadescriptor_100cats.pth'):
        models.append(('fast_megadescriptor_100cats.pth', '1024维模型'))
    
    # 检查新的优化模型
    if os.path.exists('optimized_megadescriptor_v2.pth'):
        models.append(('optimized_megadescriptor_v2.pth', '1536维优化模型'))
    
    if not models:
        print("❌ 没有找到可用的模型文件")
        return
    
    print(f"🔍 找到 {len(models)} 个模型，开始测试...")
    
    results = []
    
    for model_path, model_name in models:
        print(f"\n{'='*60}")
        print(f"🧪 测试模型: {model_name}")
        print(f"📁 文件: {model_path}")
        print(f"{'='*60}")
        
        try:
            # 测试不同策略
            strategies = ['balanced', 'adaptive']
            
            for strategy in strategies:
                print(f"\n🎯 策略: {strategy}")
                print(f"-" * 40)
                
                result = validate_optimized_model(
                    model_path=model_path,
                    dataset_path='../dataset/cat_individual_images',
                    num_cats=15,
                    strategy=strategy
                )
                
                if result:
                    results.append({
                        'model': model_name,
                        'model_path': model_path,
                        'strategy': strategy,
                        'accuracy': result['accuracy'],
                        'avg_confidence': result['avg_confidence'],
                        'avg_similarity': result['avg_similarity'],
                        'avg_stability': result.get('avg_stability', 0.0),
                        'total_cats': result['total_cats']
                    })
                    
                    print(f"✅ {strategy} 策略结果:")
                    print(f"   准确率: {result['accuracy']:.1%}")
                    print(f"   置信度: {result['avg_confidence']:.1%}")
                    print(f"   相似度: {result['avg_similarity']:.3f}")
                    if 'avg_stability' in result:
                        print(f"   稳定性: {result['avg_stability']:.3f}")
                else:
                    print(f"❌ {strategy} 策略测试失败")
                    
        except Exception as e:
            print(f"❌ 模型 {model_name} 测试失败: {e}")
            continue
    
    # 显示总结
    print(f"\n{'='*80}")
    print(f"🏆 测试总结")
    print(f"{'='*80}")
    
    if results:
        # 按准确率排序
        results.sort(key=lambda x: x['accuracy'], reverse=True)
        
        print(f"{'排名':<4} {'模型':<20} {'策略':<12} {'准确率':<8} {'置信度':<8} {'相似度':<8} {'稳定性':<8}")
        print(f"-" * 80)
        
        for i, result in enumerate(results, 1):
            model_short = result['model'][:18] + '..' if len(result['model']) > 20 else result['model']
            stability_str = f"{result['avg_stability']:.3f}" if result['avg_stability'] > 0 else "N/A"
            
            print(f"{i:<4} {model_short:<20} {result['strategy']:<12} "
                  f"{result['accuracy']:.1%}    {result['avg_confidence']:.1%}    "
                  f"{result['avg_similarity']:.3f}    {stability_str:<8}")
        
        # 找到最佳模型
        best_result = results[0]
        print(f"\n🥇 最佳模型:")
        print(f"   模型: {best_result['model']}")
        print(f"   策略: {best_result['strategy']}")
        print(f"   准确率: {best_result['accuracy']:.1%}")
        print(f"   置信度: {best_result['avg_confidence']:.1%}")
        
        # 保存结果
        with open('quick_test_v2_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n📁 详细结果已保存: quick_test_v2_results.json")
        
    else:
        print("❌ 没有成功的测试结果")

def test_specific_model(model_path: str, strategy: str = 'adaptive', num_cats: int = 20):
    """测试特定模型"""
    
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return
    
    print(f"🧪 测试模型: {model_path}")
    print(f"🎯 策略: {strategy}")
    print(f"🐱 猫咪数量: {num_cats}")
    print(f"-" * 50)
    
    try:
        result = validate_optimized_model(
            model_path=model_path,
            dataset_path='../dataset/cat_individual_images',
            num_cats=num_cats,
            strategy=strategy
        )
        
        if result:
            print(f"\n🎉 测试完成!")
            print(f"📊 准确率: {result['accuracy']:.1%}")
            print(f"📊 平均置信度: {result['avg_confidence']:.1%}")
            print(f"📊 平均相似度: {result['avg_similarity']:.3f}")
            if 'avg_stability' in result:
                print(f"📊 平均稳定性: {result['avg_stability']:.3f}")
            print(f"📊 测试猫咪: {result['total_cats']} 只")
            
            return result
        else:
            print(f"❌ 测试失败")
            return None
            
    except Exception as e:
        print(f"❌ 测试出错: {e}")
        return None

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='快速测试 V2')
    parser.add_argument('--model', type=str, default=None,
                       help='指定模型路径')
    parser.add_argument('--strategy', type=str, default='adaptive',
                       choices=['conservative', 'balanced', 'aggressive', 'adaptive'],
                       help='阈值策略')
    parser.add_argument('--cats', type=int, default=20,
                       help='测试猫咪数量')
    parser.add_argument('--all', action='store_true',
                       help='测试所有可用模型')
    
    args = parser.parse_args()
    
    if args.all:
        # 测试所有模型
        quick_test_all_models()
    elif args.model:
        # 测试指定模型
        test_specific_model(args.model, args.strategy, args.cats)
    else:
        # 默认测试所有模型
        quick_test_all_models()

if __name__ == "__main__":
    main()
