#!/usr/bin/env python3
"""
困难样本续训系统
专门针对困难样本和过度自信问题的渐进式续训方案
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
from pathlib import Path
import random
import time
import logging
import json
from typing import Dict, List, Tuple
from PIL import Image
import torchvision.transforms as transforms
from collections import defaultdict
import timm

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HardSampleTripletDataset(Dataset):
    """困难样本三元组数据集"""
    
    def __init__(self, split_file: str, corner_case_analysis: str, use_train_set: bool = True):
        self.split_file = split_file
        self.use_train_set = use_train_set
        
        # 加载数据集分割
        with open(split_file, 'r') as f:
            self.split_data = json.load(f)
        
        # 加载困难样本分析结果
        with open(corner_case_analysis, 'r') as f:
            self.corner_analysis = json.load(f)
        
        # 获取猫咪ID列表
        if use_train_set:
            self.cat_ids = self.split_data['train_cat_ids']
        else:
            self.cat_ids = self.split_data['test_cat_ids']
        
        # 构建猫咪图片映射
        self.cat_images = {}
        for cat_id in self.cat_ids:
            cat_dir = f"../dataset/cat_individual_images/{cat_id}"
            if os.path.exists(cat_dir):
                images = [f for f in os.listdir(cat_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                if images:
                    self.cat_images[cat_id] = images
        
        # 过滤掉没有图片的猫咪
        self.cat_ids = [cat_id for cat_id in self.cat_ids if cat_id in self.cat_images]
        
        # 识别困难样本对
        self.hard_pairs = self._identify_hard_pairs()
        
        # 数据增强 - 更强的增强来提高泛化能力
        self.transform = transforms.Compose([
            transforms.Resize((256, 256)),
            transforms.RandomCrop((224, 224)),
            transforms.RandomHorizontalFlip(p=0.5),
            transforms.RandomRotation(degrees=20),  # 增加旋转
            transforms.ColorJitter(brightness=0.3, contrast=0.3, saturation=0.3, hue=0.15),  # 增强颜色变化
            transforms.RandomGrayscale(p=0.1),  # 随机灰度化
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        logger.info(f"困难样本数据集构建完成: {len(self.cat_ids)} 只猫咪")
        logger.info(f"困难样本对: {len(self.hard_pairs)}")
    
    def _identify_hard_pairs(self):
        """识别困难样本对"""
        hard_pairs = []
        
        # 从分析结果中提取困难样本
        corner_cases = self.corner_analysis.get('corner_cases', [])
        cat_performance = self.corner_analysis.get('cat_performance', {})
        
        # 基于分析结果构建困难对
        for case in corner_cases:
            if case.get('issue') == 'high_inter_similarity' and 'confused_with' in case:
                cat1 = case['cat_id']
                cat2 = case['confused_with']
                if cat1 in self.cat_ids and cat2 in self.cat_ids:
                    hard_pairs.append((cat1, cat2))
        
        # 基于分离度不足构建困难对
        for cat_id, performance in cat_performance.items():
            if cat_id in self.cat_ids and performance.get('separability', 0) < 0.1:
                most_similar = performance.get('most_similar_cat')
                if most_similar and most_similar in self.cat_ids:
                    hard_pairs.append((cat_id, most_similar))
        
        # 去重
        hard_pairs = list(set(hard_pairs))
        
        # 如果困难对不足，添加一些基于ID相近的对
        if len(hard_pairs) < 50:
            for i, cat_id1 in enumerate(self.cat_ids):
                for cat_id2 in self.cat_ids[i+1:]:
                    if abs(int(cat_id1) - int(cat_id2)) < 50:
                        hard_pairs.append((cat_id1, cat_id2))
                        if len(hard_pairs) >= 100:
                            break
                if len(hard_pairs) >= 100:
                    break
        
        return hard_pairs
    
    def __len__(self):
        return len(self.cat_ids) * 20  # 减少样本数量以加快训练
    
    def __getitem__(self, idx):
        """生成困难三元组"""
        # 80%概率使用困难负样本
        use_hard_negative = random.random() < 0.8 and self.hard_pairs
        
        # 选择anchor猫咪
        anchor_cat = random.choice(self.cat_ids)
        
        # 选择positive
        anchor_img = random.choice(self.cat_images[anchor_cat])
        positive_img = random.choice(self.cat_images[anchor_cat])
        while positive_img == anchor_img and len(self.cat_images[anchor_cat]) > 1:
            positive_img = random.choice(self.cat_images[anchor_cat])
        
        # 选择negative
        if use_hard_negative:
            # 寻找与anchor_cat相关的困难对
            hard_negatives = [pair[1] for pair in self.hard_pairs if pair[0] == anchor_cat]
            hard_negatives.extend([pair[0] for pair in self.hard_pairs if pair[1] == anchor_cat])
            
            if hard_negatives:
                negative_cat = random.choice(hard_negatives)
            else:
                negative_cat = random.choice([cat for cat in self.cat_ids if cat != anchor_cat])
        else:
            negative_cat = random.choice([cat for cat in self.cat_ids if cat != anchor_cat])
        
        negative_img = random.choice(self.cat_images[negative_cat])
        
        # 加载和变换图片
        anchor_path = f"../dataset/cat_individual_images/{anchor_cat}/{anchor_img}"
        positive_path = f"../dataset/cat_individual_images/{anchor_cat}/{positive_img}"
        negative_path = f"../dataset/cat_individual_images/{negative_cat}/{negative_img}"
        
        anchor_tensor = self.transform(Image.open(anchor_path).convert('RGB'))
        positive_tensor = self.transform(Image.open(positive_path).convert('RGB'))
        negative_tensor = self.transform(Image.open(negative_path).convert('RGB'))
        
        return anchor_tensor, positive_tensor, negative_tensor

class ConfidenceAwareTripletLoss(nn.Module):
    """置信度感知的三元组损失"""
    
    def __init__(self, margin=0.5, confidence_penalty=0.3):
        super().__init__()
        self.margin = margin
        self.confidence_penalty = confidence_penalty
        self.triplet_loss = nn.TripletMarginLoss(margin=margin, reduction='none')
    
    def forward(self, anchor, positive, negative):
        # 基础三元组损失
        triplet_losses = self.triplet_loss(anchor, positive, negative)
        
        # 计算相似度
        pos_sim = torch.cosine_similarity(anchor, positive, dim=1)
        neg_sim = torch.cosine_similarity(anchor, negative, dim=1)
        
        # 置信度惩罚：当负样本相似度过高时增加损失
        confidence_penalty = torch.relu(neg_sim - 0.7) * self.confidence_penalty
        
        # 分离度奖励：当正负样本分离度好时减少损失
        separability = pos_sim - neg_sim
        separability_reward = torch.relu(separability - 0.3) * 0.1
        
        # 总损失
        total_loss = triplet_losses + confidence_penalty - separability_reward
        
        return total_loss.mean()

class HardSampleTrainer:
    """困难样本训练器"""
    
    def __init__(self, base_model_path: str, corner_case_analysis: str, split_file: str = 'dataset_split.json'):
        self.base_model_path = base_model_path
        self.corner_case_analysis = corner_case_analysis
        self.split_file = split_file
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # 数据集和数据加载器
        self.dataset = HardSampleTripletDataset(split_file, corner_case_analysis, use_train_set=True)
        self.dataloader = DataLoader(
            self.dataset,
            batch_size=4,  # 减少批次大小以节省内存
            shuffle=True,
            num_workers=2,  # 减少worker数量
            pin_memory=False  # 关闭pin_memory以节省内存
        )
        
        # 加载基础模型
        self.model = self._load_base_model()
        
        # 置信度感知损失函数
        self.criterion = ConfidenceAwareTripletLoss(margin=0.6, confidence_penalty=0.4)
        
        # 分层学习率优化器
        self.optimizer = optim.AdamW([
            {'params': self.model.backbone.parameters(), 'lr': 2e-7},  # 极低的骨干网络学习率
            {'params': self.model.feature_enhancer.parameters(), 'lr': 5e-5}  # 适中的增强网络学习率
        ], weight_decay=1e-4)
        
        # 平滑的学习率调度
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer, T_max=20, eta_min=1e-8
        )
        
        # 训练监控
        self.training_stats = {
            'losses': [],
            'separabilities': [],
            'confidences': []
        }
        
        logger.info(f"困难样本训练器初始化完成: {len(self.dataset.cat_ids)} 只猫咪")
        logger.info(f"批次数/轮: {len(self.dataloader)}")
    
    def _load_base_model(self):
        """加载基础模型"""
        checkpoint = torch.load(self.base_model_path, map_location=self.device, weights_only=False)
        
        # 根据模型类型创建模型
        model_type = checkpoint.get('model_type', 'FastMegaDescriptor')
        feature_dim = checkpoint.get('feature_dim', 1024)
        
        if model_type == 'FastMegaDescriptor':
            from fast_training_with_validation import FastMegaDescriptor
            model = FastMegaDescriptor(feature_dim=feature_dim)
        else:
            # 创建兼容模型
            model = self._create_compatible_model(feature_dim)
        
        model.load_state_dict(checkpoint['model_state_dict'])
        model = model.to(self.device)
        
        logger.info(f"成功加载基础模型: {model_type}, 特征维度: {feature_dim}")
        return model
    
    def _create_compatible_model(self, feature_dim):
        """创建兼容的模型结构"""
        class CompatibleModel(nn.Module):
            def __init__(self, feature_dim):
                super().__init__()
                self.backbone = timm.create_model(
                    'hf-hub:BVRA/MegaDescriptor-T-224',
                    pretrained=True,
                    num_classes=0
                )
                
                with torch.no_grad():
                    dummy_input = torch.randn(1, 3, 224, 224)
                    backbone_output = self.backbone(dummy_input)
                    backbone_dim = backbone_output.shape[1]
                
                self.feature_enhancer = nn.Sequential(
                    nn.Linear(backbone_dim, feature_dim * 2),
                    nn.BatchNorm1d(feature_dim * 2),
                    nn.ReLU(),
                    nn.Dropout(0.2),
                    nn.Linear(feature_dim * 2, feature_dim),
                    nn.BatchNorm1d(feature_dim),
                    nn.ReLU(),
                    nn.Dropout(0.1),
                    nn.Linear(feature_dim, feature_dim)
                )
            
            def forward(self, x):
                backbone_features = self.backbone(x)
                enhanced_features = self.feature_enhancer(backbone_features)
                return torch.nn.functional.normalize(enhanced_features, p=2, dim=1)
        
        return CompatibleModel(feature_dim)
    
    def train_epoch(self, epoch: int):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        epoch_separabilities = []
        
        for batch_idx, (anchor, positive, negative) in enumerate(self.dataloader):
            anchor = anchor.to(self.device)
            positive = positive.to(self.device)
            negative = negative.to(self.device)
            
            # 前向传播
            anchor_features = self.model(anchor)
            positive_features = self.model(positive)
            negative_features = self.model(negative)
            
            # 计算损失
            loss = self.criterion(anchor_features, positive_features, negative_features)
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=0.5)
            
            self.optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            # 计算分离度
            with torch.no_grad():
                pos_sim = torch.cosine_similarity(anchor_features, positive_features, dim=1)
                neg_sim = torch.cosine_similarity(anchor_features, negative_features, dim=1)
                separability = (pos_sim - neg_sim).mean().item()
                epoch_separabilities.append(separability)
            
            if batch_idx % 50 == 0:
                lr = self.optimizer.param_groups[1]['lr']
                logger.info(f"Epoch {epoch}, Batch {batch_idx}: Loss={loss.item():.4f}, "
                          f"Separability={separability:.4f}, LR={lr:.2e}")
        
        # 更新学习率
        self.scheduler.step()
        
        avg_loss = total_loss / num_batches if num_batches > 0 else 0.0
        avg_separability = np.mean(epoch_separabilities) if epoch_separabilities else 0.0
        
        # 记录统计
        self.training_stats['losses'].append(avg_loss)
        self.training_stats['separabilities'].append(avg_separability)
        
        return avg_loss, avg_separability

    def evaluate_progress(self):
        """评估训练进度"""
        self.model.eval()

        # 随机选择一些猫咪进行评估
        eval_cats = random.sample(self.dataset.cat_ids, min(20, len(self.dataset.cat_ids)))

        intra_similarities = []
        inter_similarities = []
        separabilities = []

        with torch.no_grad():
            for cat_id in eval_cats:
                # 获取该猫咪的图片
                cat_images = self.dataset.cat_images[cat_id][:5]  # 最多5张

                # 提取特征
                features = []
                for img_file in cat_images:
                    img_path = f"../dataset/cat_individual_images/{cat_id}/{img_file}"
                    try:
                        image = Image.open(img_path).convert('RGB')
                        image_tensor = self.dataset.transform(image).unsqueeze(0).to(self.device)
                        feature = self.model(image_tensor)
                        features.append(feature.cpu().numpy().flatten())
                    except:
                        continue

                if len(features) < 2:
                    continue

                features = np.array(features)

                # 计算内部相似性
                intra_sims = []
                for i in range(len(features)):
                    for j in range(i + 1, len(features)):
                        sim = np.dot(features[i], features[j])
                        intra_sims.append(sim)

                if intra_sims:
                    avg_intra = np.mean(intra_sims)
                    intra_similarities.append(avg_intra)

                    # 计算与其他猫咪的相似性
                    other_cat = random.choice([c for c in eval_cats if c != cat_id])
                    other_images = self.dataset.cat_images[other_cat][:3]

                    inter_sims = []
                    for other_img in other_images:
                        other_path = f"../dataset/cat_individual_images/{other_cat}/{other_img}"
                        try:
                            other_image = Image.open(other_path).convert('RGB')
                            other_tensor = self.dataset.transform(other_image).unsqueeze(0).to(self.device)
                            other_feature = self.model(other_tensor).cpu().numpy().flatten()

                            for feat in features:
                                sim = np.dot(feat, other_feature)
                                inter_sims.append(sim)
                        except:
                            continue

                    if inter_sims:
                        max_inter = np.max(inter_sims)
                        inter_similarities.append(max_inter)
                        separabilities.append(avg_intra - max_inter)

        eval_stats = {
            'avg_intra_similarity': np.mean(intra_similarities) if intra_similarities else 0.0,
            'avg_max_inter_similarity': np.mean(inter_similarities) if inter_similarities else 0.0,
            'avg_separability': np.mean(separabilities) if separabilities else 0.0,
            'separability_std': np.std(separabilities) if separabilities else 0.0
        }

        return eval_stats

    def check_training_stability(self, window_size: int = 5) -> bool:
        """检查训练稳定性"""
        if len(self.training_stats['losses']) < window_size:
            return True

        recent_losses = self.training_stats['losses'][-window_size:]
        recent_separabilities = self.training_stats['separabilities'][-window_size:]

        # 检查损失是否震荡
        loss_std = np.std(recent_losses)
        loss_trend = np.polyfit(range(window_size), recent_losses, 1)[0]  # 斜率

        # 检查分离度是否改善
        sep_trend = np.polyfit(range(window_size), recent_separabilities, 1)[0]

        # 稳定性判断
        is_stable = (
            loss_std < 0.1 and  # 损失变化不大
            loss_trend <= 0.01 and  # 损失不上升
            sep_trend >= -0.01  # 分离度不恶化
        )

        logger.info(f"训练稳定性检查: 损失标准差={loss_std:.4f}, "
                   f"损失趋势={loss_trend:.4f}, 分离度趋势={sep_trend:.4f}, "
                   f"稳定={is_stable}")

        return is_stable

    def progressive_train(self, total_epochs: int = 15, save_path: str = 'hard_sample_continued_model.pth'):
        """渐进式训练"""
        logger.info(f"开始困难样本续训: {total_epochs} epochs")

        best_separability = -1.0
        patience = 0
        max_patience = 5

        training_history = []

        for epoch in range(total_epochs):
            start_time = time.time()

            # 训练一个epoch
            avg_loss, avg_separability = self.train_epoch(epoch + 1)

            # 评估进度
            eval_stats = self.evaluate_progress()

            epoch_time = time.time() - start_time

            # 记录历史
            epoch_result = {
                'epoch': epoch + 1,
                'avg_loss': avg_loss,
                'train_separability': avg_separability,
                'eval_stats': eval_stats,
                'time': epoch_time
            }
            training_history.append(epoch_result)

            logger.info(f"Epoch {epoch + 1}/{total_epochs}:")
            logger.info(f"  训练损失: {avg_loss:.4f}")
            logger.info(f"  训练分离度: {avg_separability:.4f}")
            logger.info(f"  评估分离度: {eval_stats['avg_separability']:.4f}")
            logger.info(f"  内部相似度: {eval_stats['avg_intra_similarity']:.4f}")
            logger.info(f"  外部相似度: {eval_stats['avg_max_inter_similarity']:.4f}")
            logger.info(f"  用时: {epoch_time:.1f}s")

            # 检查训练稳定性
            if epoch >= 3:
                is_stable = self.check_training_stability()
                if not is_stable:
                    logger.warning("⚠️ 检测到训练不稳定，降低学习率")
                    for param_group in self.optimizer.param_groups:
                        param_group['lr'] *= 0.8

            # 保存最佳模型
            current_separability = eval_stats['avg_separability']
            if current_separability > best_separability:
                best_separability = current_separability
                self.save_model(save_path)
                logger.info(f"🎉 保存最佳模型: 分离度 {best_separability:.4f}")
                patience = 0
            else:
                patience += 1

            # 早停检查
            if patience >= max_patience and current_separability > 0.1:
                logger.info(f"🛑 早停: 分离度已达到 {current_separability:.4f}")
                break

            # 如果分离度持续为负，调整策略
            if current_separability < -0.05 and epoch >= 5:
                logger.warning("⚠️ 分离度持续为负，调整训练策略")
                # 降低学习率
                for param_group in self.optimizer.param_groups:
                    param_group['lr'] *= 0.5
                # 增加margin
                self.criterion.margin = min(1.0, self.criterion.margin + 0.1)
                logger.info(f"调整margin到: {self.criterion.margin}")

        logger.info(f"🚀 困难样本续训完成! 最佳分离度: {best_separability:.4f}")

        # 保存训练历史
        history_path = save_path.replace('.pth', '_history.json')
        with open(history_path, 'w') as f:
            json.dump(training_history, f, indent=2, default=str)

        return best_separability, training_history

    def save_model(self, save_path: str):
        """保存模型"""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'feature_dim': getattr(self.model, 'feature_dim', 1024),
            'model_type': 'HardSampleContinuedModel',
            'split_file': self.split_file,
            'training_stats': self.training_stats
        }, save_path, _use_new_zipfile_serialization=False)

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='困难样本续训')
    parser.add_argument('--base-model', type=str, required=True, help='基础模型路径')
    parser.add_argument('--analysis', type=str, default='corner_case_analysis.json', help='困难样本分析文件')
    parser.add_argument('--split-file', type=str, default='dataset_split.json', help='数据集分割文件')
    parser.add_argument('--epochs', type=int, default=15, help='训练轮数')
    parser.add_argument('--output', type=str, default='hard_sample_continued_model.pth', help='输出模型路径')

    args = parser.parse_args()

    # 检查文件存在性
    if not os.path.exists(args.base_model):
        print(f"❌ 基础模型文件不存在: {args.base_model}")
        return

    if not os.path.exists(args.analysis):
        print(f"❌ 分析文件不存在: {args.analysis}")
        return

    # 创建训练器
    trainer = HardSampleTrainer(args.base_model, args.analysis, args.split_file)

    # 开始续训
    best_sep, history = trainer.progressive_train(args.epochs, args.output)

    logger.info(f"🎉 困难样本续训完成! 最佳分离度: {best_sep:.4f}")
    logger.info(f"📁 模型已保存: {args.output}")

if __name__ == "__main__":
    main()
