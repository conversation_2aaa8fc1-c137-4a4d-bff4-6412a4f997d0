#!/usr/bin/env python3
"""
优化识别器 V2 - 基于成功的方法进一步改进
主要改进：
1. 更好的置信度校准
2. 自适应阈值策略
3. 多尺度特征融合
4. 更稳定的相似度计算
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
from pathlib import Path
import random
import time
import logging
import json
from typing import Dict, List, Tuple
from PIL import Image
import torchvision.transforms as transforms
import timm

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OptimizedMegaDescriptor(nn.Module):
    """优化的MegaDescriptor - 与训练时相同的架构"""
    
    def __init__(self, feature_dim=1536):
        super().__init__()
        
        # MegaDescriptor骨干网络
        self.backbone = timm.create_model(
            'hf-hub:BVRA/MegaDescriptor-T-224',
            pretrained=True,
            num_classes=0
        )
        
        # 获取骨干网络输出维度
        with torch.no_grad():
            dummy_input = torch.randn(1, 3, 224, 224)
            backbone_output = self.backbone(dummy_input)
            backbone_dim = backbone_output.shape[1]
        
        # 优化的特征增强网络
        self.feature_enhancer = nn.Sequential(
            nn.Linear(backbone_dim, feature_dim * 2),
            nn.BatchNorm1d(feature_dim * 2),
            nn.GELU(),
            nn.Dropout(0.15),
            
            nn.Linear(feature_dim * 2, feature_dim * 2),
            nn.BatchNorm1d(feature_dim * 2),
            nn.GELU(),
            nn.Dropout(0.1),
            
            nn.Linear(feature_dim * 2, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.GELU(),
            nn.Dropout(0.05),
            
            nn.Linear(feature_dim, feature_dim)
        )
        
        # 置信度校准网络
        self.confidence_calibrator = nn.Sequential(
            nn.Linear(feature_dim, feature_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(feature_dim // 2, feature_dim // 4),
            nn.ReLU(),
            nn.Dropout(0.05),
            nn.Linear(feature_dim // 4, 1),
            nn.Sigmoid()
        )
    
    def forward(self, x, return_confidence=False):
        # 骨干网络特征
        backbone_features = self.backbone(x)
        
        # 特征增强
        enhanced_features = self.feature_enhancer(backbone_features)
        
        # L2归一化
        normalized_features = torch.nn.functional.normalize(enhanced_features, p=2, dim=1)
        
        if return_confidence:
            # 置信度预测
            confidence = self.confidence_calibrator(enhanced_features)
            return normalized_features, confidence
        else:
            return normalized_features

class OptimizedCatRecognizer:
    """优化的猫咪识别器 V2"""
    
    def __init__(self, model_path: str, device='cpu', strategy: str = 'adaptive'):
        self.model_path = model_path
        self.device = device
        self.strategy = strategy
        
        # 加载模型
        self.model, self.feature_dim = self._load_model()
        
        # 多尺度图像预处理
        self.transforms = {
            'standard': transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                                   std=[0.229, 0.224, 0.225])
            ]),
            'crop_center': transforms.Compose([
                transforms.Resize((256, 256)),
                transforms.CenterCrop((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                                   std=[0.229, 0.224, 0.225])
            ]),
            'crop_random': transforms.Compose([
                transforms.Resize((256, 256)),
                transforms.RandomCrop((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                                   std=[0.229, 0.224, 0.225])
            ])
        }
        
        # 特征数据库
        self.feature_database = {}
        self.confidence_database = {}
        
        # 自适应阈值策略
        self.adaptive_thresholds = {
            'conservative': {5: 0.75, 10: 0.80, 20: 0.85, 50: 0.80},
            'balanced': {5: 0.70, 10: 0.75, 20: 0.80, 50: 0.75},
            'aggressive': {5: 0.65, 10: 0.70, 20: 0.75, 50: 0.70},
            'adaptive': {}  # 动态计算
        }
        
        logger.info(f"优化识别器初始化完成: 特征维度 {self.feature_dim}")
    
    def _load_model(self):
        """加载模型"""
        try:
            checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
            feature_dim = checkpoint.get('feature_dim', 1536)
            
            model = OptimizedMegaDescriptor(feature_dim=feature_dim).to(self.device)
            model.load_state_dict(checkpoint['model_state_dict'])
            model.eval()
            
            logger.info(f"成功加载优化模型: 特征维度 {feature_dim}")
            return model, feature_dim
            
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            raise
    
    def extract_features_multiscale(self, image_path: str):
        """多尺度特征提取"""
        try:
            image = Image.open(image_path).convert('RGB')
            
            # 提取多尺度特征
            features_list = []
            confidences_list = []
            
            for transform_name, transform in self.transforms.items():
                image_tensor = transform(image).unsqueeze(0).to(self.device)
                
                with torch.no_grad():
                    features, confidence = self.model(image_tensor, return_confidence=True)
                    features_list.append(features.cpu().numpy().flatten())
                    confidences_list.append(confidence.cpu().item())
            
            # 融合多尺度特征
            fused_features = np.mean(features_list, axis=0)
            avg_confidence = np.mean(confidences_list)
            
            # 重新归一化
            fused_features = fused_features / (np.linalg.norm(fused_features) + 1e-8)
            
            return fused_features, avg_confidence
            
        except Exception as e:
            logger.error(f"特征提取失败 {image_path}: {e}")
            return np.random.randn(self.feature_dim).astype(np.float32), 0.0
    
    def register_cat(self, cat_id: str, cat_name: str, image_paths: List[str]):
        """注册猫咪"""
        try:
            cat_features = []
            cat_confidences = []
            
            for img_path in image_paths:
                features, confidence = self.extract_features_multiscale(img_path)
                cat_features.append(features)
                cat_confidences.append(confidence)
            
            if len(cat_features) >= 3:
                self.feature_database[cat_id] = cat_features
                self.confidence_database[cat_id] = cat_confidences
                
                avg_confidence = np.mean(cat_confidences)
                logger.info(f"成功注册猫咪: {cat_name} (ID: {cat_id}), "
                          f"使用{len(cat_features)}张图片, 平均置信度: {avg_confidence:.3f}")
                
                return {'success': True, 'message': f'注册成功: {len(cat_features)}张图片'}
            else:
                return {'success': False, 'message': f'图片数量不足: {len(cat_features)}'}
                
        except Exception as e:
            logger.error(f"注册猫咪失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def compute_enhanced_similarity(self, query_features: np.ndarray, cat_id: str) -> Tuple[float, float]:
        """计算增强的相似度"""
        if cat_id not in self.feature_database:
            return 0.0, 0.0
        
        cat_features_list = self.feature_database[cat_id]
        cat_confidences = self.confidence_database[cat_id]
        
        similarities = []
        for cat_features, cat_confidence in zip(cat_features_list, cat_confidences):
            # 基础余弦相似度
            sim = np.dot(query_features, cat_features)
            
            # 置信度加权
            weighted_sim = sim * (0.7 + 0.3 * cat_confidence)
            similarities.append(weighted_sim)
        
        if not similarities:
            return 0.0, 0.0
        
        # 使用加权平均和最大值的组合
        similarities = np.array(similarities)
        sorted_sims = np.sort(similarities)[::-1]
        
        if len(sorted_sims) >= 3:
            # 前3个的加权平均
            weights = np.array([0.5, 0.3, 0.2])
            weighted_sim = np.average(sorted_sims[:3], weights=weights)
        elif len(sorted_sims) == 2:
            weights = np.array([0.7, 0.3])
            weighted_sim = np.average(sorted_sims[:2], weights=weights)
        else:
            weighted_sim = sorted_sims[0]
        
        # 计算稳定性（相似度的标准差，越小越稳定）
        stability = 1.0 / (1.0 + np.std(similarities))
        
        return float(weighted_sim), float(stability)
    
    def get_adaptive_threshold(self, num_cats: int, similarities: List[float] = None) -> float:
        """获取自适应阈值"""
        if self.strategy == 'adaptive' and similarities:
            # 基于相似度分布动态计算阈值
            similarities = np.array(similarities)
            mean_sim = np.mean(similarities)
            std_sim = np.std(similarities)
            
            # 动态阈值：均值 + 0.5 * 标准差
            adaptive_threshold = mean_sim + 0.5 * std_sim
            
            # 限制在合理范围内
            adaptive_threshold = np.clip(adaptive_threshold, 0.6, 0.9)
            
            return adaptive_threshold
        else:
            # 使用预设阈值策略
            thresholds = self.adaptive_thresholds[self.strategy]
            
            sorted_scales = sorted(thresholds.keys())
            
            if num_cats <= sorted_scales[0]:
                return thresholds[sorted_scales[0]]
            elif num_cats >= sorted_scales[-1]:
                return thresholds[sorted_scales[-1]]
            else:
                # 线性插值
                for i in range(len(sorted_scales) - 1):
                    if sorted_scales[i] <= num_cats <= sorted_scales[i + 1]:
                        x1, y1 = sorted_scales[i], thresholds[sorted_scales[i]]
                        x2, y2 = sorted_scales[i + 1], thresholds[sorted_scales[i + 1]]
                        return y1 + (y2 - y1) * (num_cats - x1) / (x2 - x1)
            
            return 0.75  # 默认值
    
    def recognize_cat(self, image_path: str):
        """识别猫咪"""
        try:
            # 提取查询特征
            query_features, query_confidence = self.extract_features_multiscale(image_path)
            
            # 计算与所有注册猫咪的相似度
            matches = []
            similarities_for_threshold = []
            
            for cat_id in self.feature_database:
                similarity, stability = self.compute_enhanced_similarity(query_features, cat_id)
                matches.append((cat_id, similarity, stability))
                similarities_for_threshold.append(similarity)
            
            if not matches:
                return {
                    'success': False,
                    'message': '没有注册的猫咪',
                    'status': 'no_cats'
                }
            
            # 排序找到最佳匹配
            matches.sort(key=lambda x: x[1], reverse=True)
            best_match_id, best_similarity, best_stability = matches[0]
            
            # 获取自适应阈值
            num_cats = len(self.feature_database)
            adaptive_threshold = self.get_adaptive_threshold(num_cats, similarities_for_threshold)
            
            # 校准置信度
            calibrated_confidence = min(0.99, best_similarity * query_confidence * best_stability)
            
            if best_similarity >= adaptive_threshold:
                return {
                    'success': True,
                    'cat_id': best_match_id,
                    'confidence': calibrated_confidence,
                    'similarity': best_similarity,
                    'stability': best_stability,
                    'raw_confidence': query_confidence,
                    'threshold_used': adaptive_threshold,
                    'strategy_used': self.strategy,
                    'status': 'recognized'
                }
            else:
                return {
                    'success': False,
                    'message': f'相似度过低 ({best_similarity:.3f} < {adaptive_threshold:.3f})',
                    'status': 'unknown',
                    'best_match': {
                        'cat_id': best_match_id,
                        'similarity': best_similarity,
                        'stability': best_stability,
                        'raw_confidence': query_confidence
                    },
                    'threshold_used': adaptive_threshold,
                    'strategy_used': self.strategy
                }
                
        except Exception as e:
            logger.error(f"识别猫咪失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

def validate_optimized_model(model_path: str, dataset_path: str, num_cats: int = 20, strategy: str = 'adaptive'):
    """验证优化模型"""
    logger.info(f"🔍 验证优化模型: {model_path}")
    
    # 加载数据集分割信息
    with open('dataset_split.json', 'r') as f:
        split_info = json.load(f)
    
    test_cats = split_info['test_data']
    test_cat_ids = [cat['cat_id'] for cat in test_cats]
    
    # 构建测试集图片字典
    test_cat_images = {}
    for cat in test_cats:
        test_cat_images[cat['cat_id']] = cat['images']
    
    # 创建识别器
    recognizer = OptimizedCatRecognizer(model_path, device='cpu', strategy=strategy)
    
    # 随机选择测试猫咪
    if len(test_cat_ids) < num_cats:
        selected_cats = test_cat_ids
    else:
        selected_cats = random.sample(test_cat_ids, num_cats)
    
    # 注册猫咪
    registered_cats = []
    for cat_id in selected_cats:
        if cat_id not in test_cat_images:
            continue
        
        images = test_cat_images[cat_id]
        if len(images) < 5:
            continue
        
        # 分割注册和测试图片
        train_count = max(3, int(len(images) * 0.7))
        train_images = images[:train_count]
        test_images = images[train_count:]
        
        # 注册猫咪
        result = recognizer.register_cat(cat_id, f"Cat_{cat_id}", train_images)
        if result['success'] and test_images:
            registered_cats.append((cat_id, test_images))
    
    if len(registered_cats) < 3:
        logger.warning(f"注册成功的猫咪数量不足: {len(registered_cats)}")
        return None
    
    # 识别测试
    correct = 0
    total = 0
    confidences = []
    similarities = []
    stabilities = []
    
    for cat_id, test_images in registered_cats:
        test_image = random.choice(test_images)
        result = recognizer.recognize_cat(test_image)
        
        total += 1
        is_correct = result.get('success') and result.get('cat_id') == cat_id
        
        if is_correct:
            correct += 1
            confidences.append(result.get('confidence', 0.0))
        
        similarities.append(result.get('similarity', 0.0))
        stabilities.append(result.get('stability', 0.0))
        
        # 显示结果
        status = "✅" if is_correct else "❌"
        predicted = result.get('cat_id', 'unknown')
        similarity = result.get('similarity', 0.0)
        confidence = result.get('confidence', 0.0)
        stability = result.get('stability', 0.0)
        
        logger.info(f"{status} 真实:{cat_id} 预测:{predicted} "
                  f"相似度:{similarity:.3f} 置信度:{confidence:.1%} 稳定性:{stability:.3f}")
    
    # 计算统计
    accuracy = correct / total if total > 0 else 0.0
    avg_confidence = np.mean(confidences) if confidences else 0.0
    avg_similarity = np.mean(similarities) if similarities else 0.0
    avg_stability = np.mean(stabilities) if stabilities else 0.0
    
    print(f"\n🎉 优化模型验证结果:")
    print(f"📊 准确率: {accuracy:.1%} ({correct}/{total})")
    print(f"📊 平均置信度: {avg_confidence:.1%}")
    print(f"📊 平均相似度: {avg_similarity:.3f}")
    print(f"📊 平均稳定性: {avg_stability:.3f}")
    print(f"📊 测试规模: {len(registered_cats)} 只猫咪")
    
    return {
        'accuracy': accuracy,
        'avg_confidence': avg_confidence,
        'avg_similarity': avg_similarity,
        'avg_stability': avg_stability,
        'total_cats': len(registered_cats)
    }

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='优化识别器验证')
    parser.add_argument('--model', type=str, default='optimized_megadescriptor_v2.pth',
                       help='模型路径')
    parser.add_argument('--dataset', type=str, 
                       default='../dataset/cat_individual_images',
                       help='数据集路径')
    parser.add_argument('--cats', type=int, default=20,
                       help='测试猫咪数量')
    parser.add_argument('--strategy', type=str, default='adaptive',
                       choices=['conservative', 'balanced', 'aggressive', 'adaptive'],
                       help='阈值策略')
    
    args = parser.parse_args()
    
    # 运行验证
    result = validate_optimized_model(
        model_path=args.model,
        dataset_path=args.dataset,
        num_cats=args.cats,
        strategy=args.strategy
    )
    
    if result:
        print(f"\n🎉 验证完成! 准确率: {result['accuracy']:.1%}")
    else:
        print(f"\n❌ 验证失败")

if __name__ == "__main__":
    main()
