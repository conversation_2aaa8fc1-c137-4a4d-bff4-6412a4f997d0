#!/usr/bin/env python3
"""
快速验证脚本 - 测试现有1024维模型在独立测试集上的表现
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
from pathlib import Path
import random
import time
import logging
import json
from typing import Dict, List, Tuple
from PIL import Image
import torchvision.transforms as transforms
import timm

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FlexibleCatRecognizer:
    """灵活的猫咪识别器 - 兼容不同模型架构"""
    
    def __init__(self, model_path: str, device='cpu', strategy: str = 'balanced'):
        self.model_path = model_path
        self.device = device
        self.strategy = strategy
        
        # 加载模型
        self.model, self.feature_dim = self._load_model()
        
        # 图像预处理
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        # 特征数据库
        self.feature_database = {}
        
        # 阈值策略
        self.threshold_strategies = {
            'conservative': {5: 0.70, 10: 0.75, 20: 0.80, 50: 0.75},
            'balanced': {5: 0.65, 10: 0.70, 20: 0.75, 50: 0.70},
            'aggressive': {5: 0.75, 10: 0.80, 20: 0.85, 50: 0.80}
        }
        
        logger.info(f"灵活识别器初始化完成: 特征维度 {self.feature_dim}")
    
    def _load_model(self):
        """加载模型"""
        try:
            checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
            feature_dim = checkpoint.get('feature_dim', 1024)
            
            # 创建模型
            if feature_dim == 1024:
                # 1024维模型
                model = self._create_1024_model()
            elif feature_dim == 2048:
                # 2048维模型
                model = self._create_2048_model()
            elif feature_dim == 1536:
                # 1536维模型
                model = self._create_1536_model()
            else:
                raise ValueError(f"不支持的特征维度: {feature_dim}")
            
            model.load_state_dict(checkpoint['model_state_dict'])
            model.to(self.device)
            model.eval()
            
            logger.info(f"成功加载模型: {feature_dim}维")
            return model, feature_dim
            
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            raise
    
    def _create_1024_model(self):
        """创建1024维模型"""
        class FastMegaDescriptor(nn.Module):
            def __init__(self):
                super().__init__()
                self.backbone = timm.create_model(
                    'hf-hub:BVRA/MegaDescriptor-T-224',
                    pretrained=True,
                    num_classes=0
                )
                
                with torch.no_grad():
                    dummy_input = torch.randn(1, 3, 224, 224)
                    backbone_output = self.backbone(dummy_input)
                    backbone_dim = backbone_output.shape[1]
                
                self.feature_enhancer = nn.Sequential(
                    nn.Linear(backbone_dim, 1024 * 2),
                    nn.BatchNorm1d(1024 * 2),
                    nn.ReLU(),
                    nn.Dropout(0.1),
                    nn.Linear(1024 * 2, 1024),
                    nn.BatchNorm1d(1024),
                    nn.ReLU(),
                    nn.Dropout(0.05),
                    nn.Linear(1024, 1024)
                )
            
            def forward(self, x):
                backbone_features = self.backbone(x)
                enhanced_features = self.feature_enhancer(backbone_features)
                return torch.nn.functional.normalize(enhanced_features, p=2, dim=1)
        
        return FastMegaDescriptor()
    
    def _create_2048_model(self):
        """创建2048维模型（占位符）"""
        # 这里应该是2048维模型的架构
        # 由于当前没有成功训练的2048维模型，暂时返回None
        raise NotImplementedError("2048维模型架构未实现")
    
    def _create_1536_model(self):
        """创建1536维模型（占位符）"""
        # 这里应该是1536维模型的架构
        # 等稳定训练完成后实现
        raise NotImplementedError("1536维模型架构未实现")
    
    def extract_features(self, image_path: str):
        """提取特征"""
        try:
            image = Image.open(image_path).convert('RGB')
            image_tensor = self.transform(image).unsqueeze(0).to(self.device)
            
            with torch.no_grad():
                features = self.model(image_tensor)
                return features.cpu().numpy().flatten()
        except Exception as e:
            logger.error(f"特征提取失败 {image_path}: {e}")
            return np.random.randn(self.feature_dim).astype(np.float32)
    
    def register_cat(self, cat_id: str, cat_name: str, image_paths: List[str]):
        """注册猫咪"""
        try:
            cat_features = []
            for img_path in image_paths:
                features = self.extract_features(img_path)
                cat_features.append(features)
            
            if len(cat_features) >= 3:
                self.feature_database[cat_id] = cat_features
                logger.info(f"成功注册猫咪: {cat_name} (ID: {cat_id}), 使用{len(cat_features)}张图片")
                return {'success': True, 'message': f'注册成功: {len(cat_features)}张图片'}
            else:
                return {'success': False, 'message': f'图片数量不足: {len(cat_features)}'}
                
        except Exception as e:
            logger.error(f"注册猫咪失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def compute_similarity(self, query_features: np.ndarray, cat_id: str) -> float:
        """计算相似度"""
        if cat_id not in self.feature_database:
            return 0.0
        
        cat_features_list = self.feature_database[cat_id]
        similarities = []
        
        for cat_features in cat_features_list:
            sim = np.dot(query_features, cat_features)
            similarities.append(sim)
        
        if not similarities:
            return 0.0
        
        # 使用加权平均
        similarities = np.array(similarities)
        sorted_sims = np.sort(similarities)[::-1]
        
        if len(sorted_sims) >= 3:
            weights = np.array([0.5, 0.3, 0.2])
            weighted_sim = np.average(sorted_sims[:3], weights=weights)
        elif len(sorted_sims) == 2:
            weights = np.array([0.7, 0.3])
            weighted_sim = np.average(sorted_sims[:2], weights=weights)
        else:
            weighted_sim = sorted_sims[0]
        
        return float(weighted_sim)
    
    def get_adaptive_threshold(self, num_cats: int) -> float:
        """获取自适应阈值"""
        thresholds = self.threshold_strategies[self.strategy]
        
        sorted_scales = sorted(thresholds.keys())
        
        if num_cats <= sorted_scales[0]:
            return thresholds[sorted_scales[0]]
        elif num_cats >= sorted_scales[-1]:
            return thresholds[sorted_scales[-1]]
        else:
            for i in range(len(sorted_scales) - 1):
                if sorted_scales[i] <= num_cats <= sorted_scales[i + 1]:
                    x1, y1 = sorted_scales[i], thresholds[sorted_scales[i]]
                    x2, y2 = sorted_scales[i + 1], thresholds[sorted_scales[i + 1]]
                    return y1 + (y2 - y1) * (num_cats - x1) / (x2 - x1)
        
        return 0.70
    
    def recognize_cat(self, image_path: str):
        """识别猫咪"""
        try:
            query_features = self.extract_features(image_path)
            
            matches = []
            for cat_id in self.feature_database:
                similarity = self.compute_similarity(query_features, cat_id)
                matches.append((cat_id, similarity))
            
            if not matches:
                return {'success': False, 'message': '没有注册的猫咪'}
            
            matches.sort(key=lambda x: x[1], reverse=True)
            best_match_id, best_similarity = matches[0]
            
            num_cats = len(self.feature_database)
            adaptive_threshold = self.get_adaptive_threshold(num_cats)
            
            if best_similarity >= adaptive_threshold:
                return {
                    'success': True,
                    'cat_id': best_match_id,
                    'confidence': min(0.99, best_similarity * 1.1),
                    'similarity': best_similarity,
                    'threshold_used': adaptive_threshold
                }
            else:
                return {
                    'success': False,
                    'message': f'相似度过低 ({best_similarity:.3f} < {adaptive_threshold:.3f})',
                    'best_match': {'cat_id': best_match_id, 'similarity': best_similarity},
                    'threshold_used': adaptive_threshold
                }
                
        except Exception as e:
            logger.error(f"识别猫咪失败: {e}")
            return {'success': False, 'error': str(e)}

def quick_test_on_independent_set(model_path: str, split_file: str = 'dataset_split.json', 
                                 num_cats: int = 15, rounds: int = 3):
    """在独立测试集上快速测试"""
    logger.info(f"🔍 快速独立测试: {model_path}")
    
    # 加载数据集分割信息
    with open(split_file, 'r') as f:
        split_info = json.load(f)
    
    test_cats = split_info['test_data']
    test_cat_ids = [cat['cat_id'] for cat in test_cats]
    
    # 构建测试集图片字典
    test_cat_images = {}
    for cat in test_cats:
        test_cat_images[cat['cat_id']] = cat['images']
    
    # 创建识别器
    recognizer = FlexibleCatRecognizer(model_path, device='cpu', strategy='balanced')
    
    all_results = []
    
    for round_num in range(rounds):
        logger.info(f"第 {round_num + 1} 轮...")
        
        # 随机选择测试猫咪
        if len(test_cat_ids) < num_cats:
            selected_cats = test_cat_ids
        else:
            selected_cats = random.sample(test_cat_ids, num_cats)
        
        # 注册猫咪
        registered_cats = []
        for cat_id in selected_cats:
            if cat_id not in test_cat_images:
                continue
            
            images = test_cat_images[cat_id]
            if len(images) < 5:
                continue
            
            # 分割注册和测试图片
            train_count = max(3, int(len(images) * 0.7))
            train_images = images[:train_count]
            test_images = images[train_count:]
            
            # 注册猫咪
            result = recognizer.register_cat(cat_id, f"Cat_{cat_id}", train_images)
            if result['success'] and test_images:
                registered_cats.append((cat_id, test_images))
        
        if len(registered_cats) < 3:
            logger.warning(f"注册成功的猫咪数量不足: {len(registered_cats)}")
            continue
        
        # 识别测试
        correct = 0
        total = 0
        confidences = []
        similarities = []
        
        for cat_id, test_images in registered_cats:
            test_image = random.choice(test_images)
            result = recognizer.recognize_cat(test_image)
            
            total += 1
            is_correct = result.get('success') and result.get('cat_id') == cat_id
            
            if is_correct:
                correct += 1
                confidences.append(result.get('confidence', 0.0))
            
            similarities.append(result.get('similarity', 0.0))
            
            # 显示结果
            status = "✅" if is_correct else "❌"
            predicted = result.get('cat_id', 'unknown')
            similarity = result.get('similarity', 0.0)
            confidence = result.get('confidence', 0.0)
            
            logger.info(f"{status} 真实:{cat_id} 预测:{predicted} "
                      f"相似度:{similarity:.3f} 置信度:{confidence:.1%}")
        
        # 计算轮次统计
        accuracy = correct / total if total > 0 else 0.0
        avg_confidence = np.mean(confidences) if confidences else 0.0
        avg_similarity = np.mean(similarities) if similarities else 0.0
        
        round_result = {
            'round': round_num + 1,
            'accuracy': accuracy,
            'correct': correct,
            'total': total,
            'avg_confidence': avg_confidence,
            'avg_similarity': avg_similarity
        }
        
        all_results.append(round_result)
        
        logger.info(f"轮次结果: 准确率 {accuracy:.1%} ({correct}/{total})")
        
        # 清空特征数据库为下一轮做准备
        recognizer.feature_database.clear()
    
    # 显示总结
    if all_results:
        accuracies = [r['accuracy'] for r in all_results]
        confidences = [r['avg_confidence'] for r in all_results]
        similarities = [r['avg_similarity'] for r in all_results]
        
        print(f"\n" + "=" * 60)
        print(f"🎯 快速独立测试总结")
        print("=" * 60)
        print(f"模型: {model_path}")
        print(f"测试规模: {num_cats} 只猫咪")
        print(f"测试轮数: {len(all_results)}")
        print(f"")
        print(f"📊 性能统计:")
        print(f"   平均准确率: {np.mean(accuracies):.1%} ± {np.std(accuracies):.1%}")
        print(f"   准确率范围: {np.min(accuracies):.1%} - {np.max(accuracies):.1%}")
        print(f"   平均置信度: {np.mean(confidences):.1%}")
        print(f"   平均相似度: {np.mean(similarities):.3f}")
        
        # 评价
        avg_accuracy = np.mean(accuracies)
        if avg_accuracy >= 0.95:
            rating = "🌟 优秀"
        elif avg_accuracy >= 0.90:
            rating = "✅ 良好"
        elif avg_accuracy >= 0.85:
            rating = "📈 不错"
        else:
            rating = "⚠️ 需要改进"
        
        print(f"\n📊 评价: {rating}")
        
        return {
            'avg_accuracy': np.mean(accuracies),
            'accuracy_std': np.std(accuracies),
            'results': all_results
        }
    
    return None

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='快速验证')
    parser.add_argument('--model', type=str, default='fast_megadescriptor_100cats_best.pth',
                       help='模型路径')
    parser.add_argument('--split-file', type=str, default='dataset_split.json',
                       help='数据集分割文件')
    parser.add_argument('--cats', type=int, default=15,
                       help='测试猫咪数量')
    parser.add_argument('--rounds', type=int, default=3,
                       help='测试轮数')
    
    args = parser.parse_args()
    
    # 运行快速测试
    result = quick_test_on_independent_set(
        model_path=args.model,
        split_file=args.split_file,
        num_cats=args.cats,
        rounds=args.rounds
    )
    
    if result:
        print(f"\n🎉 快速验证完成! 平均准确率: {result['avg_accuracy']:.1%}")
    else:
        print(f"\n❌ 快速验证失败")

if __name__ == "__main__":
    main()
