#!/usr/bin/env python3
"""
困难样本和过度自信问题分析器
用于识别模型的corner cases和置信度校准问题
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
from pathlib import Path
import random
import logging
import json
from typing import Dict, List, Tuple
from PIL import Image
import torchvision.transforms as transforms
from collections import defaultdict
import timm
import matplotlib.pyplot as plt
import seaborn as sns

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ModelDiagnostics:
    """模型诊断工具"""
    
    def __init__(self, model_path: str, split_file: str = 'dataset_split.json'):
        self.model_path = model_path
        self.split_file = split_file
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # 加载数据集信息
        with open(split_file, 'r') as f:
            self.split_data = json.load(f)
        
        # 图像变换
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        # 加载模型
        self.model = self._load_model()
        self.model.eval()
        
        logger.info(f"模型诊断器初始化完成: {model_path}")
    
    def _load_model(self):
        """加载模型"""
        checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
        
        # 根据模型类型创建模型
        model_type = checkpoint.get('model_type', 'FastMegaDescriptor')
        feature_dim = checkpoint.get('feature_dim', 1024)
        
        if model_type == 'FastMegaDescriptor':
            from fast_training_with_validation import FastMegaDescriptor
            model = FastMegaDescriptor(feature_dim=feature_dim)
        else:
            # 默认使用灵活模型
            model = self._create_flexible_model(feature_dim)
        
        model.load_state_dict(checkpoint['model_state_dict'])
        return model.to(self.device)
    
    def _create_flexible_model(self, feature_dim):
        """创建灵活的模型结构"""
        class FlexibleModel(nn.Module):
            def __init__(self, feature_dim):
                super().__init__()
                self.backbone = timm.create_model(
                    'hf-hub:BVRA/MegaDescriptor-T-224',
                    pretrained=True,
                    num_classes=0
                )
                
                with torch.no_grad():
                    dummy_input = torch.randn(1, 3, 224, 224)
                    backbone_output = self.backbone(dummy_input)
                    backbone_dim = backbone_output.shape[1]
                
                self.feature_enhancer = nn.Sequential(
                    nn.Linear(backbone_dim, feature_dim * 2),
                    nn.BatchNorm1d(feature_dim * 2),
                    nn.ReLU(),
                    nn.Dropout(0.2),
                    nn.Linear(feature_dim * 2, feature_dim),
                    nn.BatchNorm1d(feature_dim),
                    nn.ReLU(),
                    nn.Dropout(0.1),
                    nn.Linear(feature_dim, feature_dim)
                )
            
            def forward(self, x):
                backbone_features = self.backbone(x)
                enhanced_features = self.feature_enhancer(backbone_features)
                return torch.nn.functional.normalize(enhanced_features, p=2, dim=1)
        
        return FlexibleModel(feature_dim)
    
    def extract_features(self, image_path: str) -> np.ndarray:
        """提取图像特征"""
        try:
            image = Image.open(image_path).convert('RGB')
            image_tensor = self.transform(image).unsqueeze(0).to(self.device)
            
            with torch.no_grad():
                features = self.model(image_tensor)
                return features.cpu().numpy().flatten()
        except Exception as e:
            logger.error(f"特征提取失败 {image_path}: {e}")
            return None
    
    def analyze_corner_cases(self, num_cats: int = 50, samples_per_cat: int = 10) -> Dict:
        """分析困难样本"""
        logger.info(f"开始分析困难样本: {num_cats} 只猫咪, 每只 {samples_per_cat} 张图片")

        # 选择测试猫咪
        test_cat_ids = self.split_data.get('test_cat_ids', [])
        if not test_cat_ids:
            logger.error("数据集分割文件中没有找到test_cat_ids")
            return {}

        test_cats = random.sample(test_cat_ids, min(num_cats, len(test_cat_ids)))
        
        # 存储分析结果
        results = {
            'corner_cases': [],
            'confidence_issues': [],
            'similarity_distribution': [],
            'cat_performance': {}
        }
        
        # 为每只猫咪构建特征数据库
        cat_features = {}
        for cat_id in test_cats:
            # 直接从文件夹读取图片
            cat_dir = f"../dataset/cat_individual_images/{cat_id}"
            if not os.path.exists(cat_dir):
                logger.warning(f"猫咪目录不存在: {cat_dir}")
                continue

            # 获取图片文件
            image_files = [f for f in os.listdir(cat_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
            selected_images = random.sample(image_files, min(samples_per_cat, len(image_files)))

            features_list = []
            for img_file in selected_images:
                full_path = os.path.join(cat_dir, img_file)
                features = self.extract_features(full_path)
                if features is not None:
                    features_list.append(features)

            if features_list:
                cat_features[cat_id] = np.array(features_list)
        
        logger.info(f"成功提取 {len(cat_features)} 只猫咪的特征")
        
        # 分析每只猫咪的内部相似性和与其他猫咪的混淆情况
        for cat_id, features in cat_features.items():
            cat_analysis = self._analyze_single_cat(cat_id, features, cat_features)
            results['cat_performance'][cat_id] = cat_analysis
            
            # 识别困难样本
            if cat_analysis['intra_similarity'] < 0.7:  # 内部相似度低
                results['corner_cases'].append({
                    'cat_id': cat_id,
                    'issue': 'low_intra_similarity',
                    'value': cat_analysis['intra_similarity'],
                    'description': '同一只猫咪的图片特征差异过大'
                })
            
            if cat_analysis['max_inter_similarity'] > 0.8:  # 与其他猫咪相似度高
                results['corner_cases'].append({
                    'cat_id': cat_id,
                    'issue': 'high_inter_similarity',
                    'value': cat_analysis['max_inter_similarity'],
                    'confused_with': cat_analysis['most_similar_cat'],
                    'description': '与其他猫咪特征过于相似'
                })
            
            # 置信度问题
            separability = cat_analysis['intra_similarity'] - cat_analysis['max_inter_similarity']
            if separability < 0.1:  # 分离度不足
                results['confidence_issues'].append({
                    'cat_id': cat_id,
                    'separability': separability,
                    'description': '特征分离度不足，容易产生过度自信的错误预测'
                })
        
        # 计算整体统计
        results['overall_stats'] = self._calculate_overall_stats(results['cat_performance'])
        
        logger.info(f"困难样本分析完成: 发现 {len(results['corner_cases'])} 个困难样本")
        logger.info(f"置信度问题: {len(results['confidence_issues'])} 个")
        
        return results
    
    def _analyze_single_cat(self, cat_id: str, features: np.ndarray, all_cat_features: Dict) -> Dict:
        """分析单只猫咪的特征"""
        # 计算内部相似性（同一只猫咪不同图片间的相似性）
        intra_similarities = []
        for i in range(len(features)):
            for j in range(i + 1, len(features)):
                sim = np.dot(features[i], features[j])
                intra_similarities.append(sim)
        
        intra_similarity = np.mean(intra_similarities) if intra_similarities else 0.0
        
        # 计算与其他猫咪的相似性
        inter_similarities = []
        most_similar_cat = None
        max_inter_similarity = 0.0
        
        for other_cat_id, other_features in all_cat_features.items():
            if other_cat_id == cat_id:
                continue
            
            # 计算与其他猫咪的最大相似性
            max_sim_with_other = 0.0
            for feat1 in features:
                for feat2 in other_features:
                    sim = np.dot(feat1, feat2)
                    max_sim_with_other = max(max_sim_with_other, sim)
            
            inter_similarities.append(max_sim_with_other)
            
            if max_sim_with_other > max_inter_similarity:
                max_inter_similarity = max_sim_with_other
                most_similar_cat = other_cat_id
        
        return {
            'intra_similarity': intra_similarity,
            'intra_std': np.std(intra_similarities) if intra_similarities else 0.0,
            'max_inter_similarity': max_inter_similarity,
            'avg_inter_similarity': np.mean(inter_similarities) if inter_similarities else 0.0,
            'most_similar_cat': most_similar_cat,
            'separability': intra_similarity - max_inter_similarity
        }
    
    def _calculate_overall_stats(self, cat_performance: Dict) -> Dict:
        """计算整体统计信息"""
        intra_sims = [perf['intra_similarity'] for perf in cat_performance.values()]
        inter_sims = [perf['max_inter_similarity'] for perf in cat_performance.values()]
        separabilities = [perf['separability'] for perf in cat_performance.values()]
        
        return {
            'avg_intra_similarity': np.mean(intra_sims),
            'std_intra_similarity': np.std(intra_sims),
            'avg_max_inter_similarity': np.mean(inter_sims),
            'std_max_inter_similarity': np.std(inter_sims),
            'avg_separability': np.mean(separabilities),
            'std_separability': np.std(separabilities),
            'poor_separability_count': sum(1 for s in separabilities if s < 0.1),
            'good_separability_count': sum(1 for s in separabilities if s > 0.3)
        }
    
    def generate_report(self, results: Dict, output_path: str = 'corner_case_analysis.json'):
        """生成分析报告"""
        # 保存详细结果
        with open(output_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        # 打印摘要报告
        print("\n" + "="*60)
        print("🔍 困难样本和过度自信问题分析报告")
        print("="*60)
        
        stats = results['overall_stats']
        print(f"\n📊 整体统计:")
        print(f"   平均内部相似度: {stats['avg_intra_similarity']:.3f} ± {stats['std_intra_similarity']:.3f}")
        print(f"   平均最大外部相似度: {stats['avg_max_inter_similarity']:.3f} ± {stats['std_max_inter_similarity']:.3f}")
        print(f"   平均分离度: {stats['avg_separability']:.3f} ± {stats['std_separability']:.3f}")
        
        print(f"\n⚠️ 问题统计:")
        print(f"   困难样本数量: {len(results['corner_cases'])}")
        print(f"   置信度问题数量: {len(results['confidence_issues'])}")
        print(f"   分离度不足(<0.1): {stats['poor_separability_count']}")
        print(f"   分离度良好(>0.3): {stats['good_separability_count']}")
        
        # 显示最严重的困难样本
        if results['corner_cases']:
            print(f"\n🚨 最严重的困难样本:")
            sorted_cases = sorted(results['corner_cases'], 
                                key=lambda x: x.get('value', 0), reverse=True)[:5]
            for i, case in enumerate(sorted_cases, 1):
                value = case.get('value', 'N/A')
                if isinstance(value, (int, float)):
                    value_str = f"{value:.3f}"
                else:
                    value_str = str(value)
                print(f"   {i}. 猫咪 {case['cat_id']}: {case['description']} (值: {value_str})")
        
        print(f"\n📁 详细报告已保存: {output_path}")
        
        return results

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='困难样本分析')
    parser.add_argument('--model', type=str, required=True, help='模型文件路径')
    parser.add_argument('--split-file', type=str, default='dataset_split.json', help='数据集分割文件')
    parser.add_argument('--cats', type=int, default=50, help='分析的猫咪数量')
    parser.add_argument('--samples', type=int, default=10, help='每只猫咪的样本数量')
    parser.add_argument('--output', type=str, default='corner_case_analysis.json', help='输出文件')
    
    args = parser.parse_args()
    
    # 检查模型文件
    if not os.path.exists(args.model):
        print(f"❌ 模型文件不存在: {args.model}")
        return
    
    # 创建诊断器
    diagnostics = ModelDiagnostics(args.model, args.split_file)
    
    # 分析困难样本
    results = diagnostics.analyze_corner_cases(args.cats, args.samples)
    
    # 生成报告
    diagnostics.generate_report(results, args.output)

if __name__ == "__main__":
    main()
