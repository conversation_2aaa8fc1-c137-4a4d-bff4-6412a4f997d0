#!/usr/bin/env python3
"""
稳定优化训练系统 V2 - 修复震荡问题
主要修复：
1. 降低学习率，避免震荡
2. 减少困难负样本比例
3. 简化损失函数
4. 更保守的训练策略
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
from pathlib import Path
import random
import time
import logging
import json
from typing import Dict, List, Tuple
from PIL import Image
import torchvision.transforms as transforms
from collections import defaultdict
import timm

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class StableTripletDataset(Dataset):
    """稳定的三元组数据集 - 减少困难样本比例"""
    
    def __init__(self, split_file: str = 'dataset_split.json', use_train_set: bool = True):
        # 加载数据集分割信息
        with open(split_file, 'r') as f:
            split_info = json.load(f)
        
        self.cats_data = split_info['train_data'] if use_train_set else split_info['test_data']
        self.cat_ids = [cat['cat_id'] for cat in self.cats_data]
        
        # 构建猫咪图片字典
        self.cat_images = {}
        for cat in self.cats_data:
            self.cat_images[cat['cat_id']] = cat['images']
        
        # 稳定的数据增强策略
        self.transform = transforms.Compose([
            transforms.Resize((256, 256)),
            transforms.RandomCrop((224, 224)),
            transforms.RandomHorizontalFlip(p=0.5),
            transforms.RandomRotation(degrees=15),  # 减少旋转角度
            transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),  # 减少颜色变化
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        # 计算困难负样本对 - 减少数量
        self.hard_negative_pairs = self._compute_hard_negatives()
        
        dataset_type = "训练集" if use_train_set else "测试集"
        logger.info(f"稳定数据集构建完成 ({dataset_type}): {len(self.cat_ids)} 只猫咪")
        logger.info(f"总图片数: {sum(len(imgs) for imgs in self.cat_images.values())}")
        logger.info(f"困难负样本对: {len(self.hard_negative_pairs)}")
    
    def _compute_hard_negatives(self):
        """计算困难负样本对 - 更保守的策略"""
        hard_pairs = []
        
        # 只选择ID非常相近的作为困难样本
        for i, cat_id1 in enumerate(self.cat_ids):
            for j, cat_id2 in enumerate(self.cat_ids[i+1:], i+1):
                id_diff = abs(int(cat_id1) - int(cat_id2))
                
                # 只有ID差异很小的才作为困难样本
                if id_diff < 30:  # 从80减少到30
                    hard_pairs.append((cat_id1, cat_id2))
        
        return hard_pairs
    
    def __len__(self):
        return len(self.cat_ids) * 80  # 减少样本数量
    
    def __getitem__(self, idx):
        """生成稳定的三元组"""
        # 只有30%概率使用困难负样本 - 从70%降低到30%
        use_hard_negative = random.random() < 0.3 and self.hard_negative_pairs
        
        # 选择anchor猫咪
        anchor_cat = random.choice(self.cat_ids)
        
        # 选择positive
        anchor_img = random.choice(self.cat_images[anchor_cat])
        positive_img = random.choice(self.cat_images[anchor_cat])
        while positive_img == anchor_img and len(self.cat_images[anchor_cat]) > 1:
            positive_img = random.choice(self.cat_images[anchor_cat])
        
        # 选择negative
        if use_hard_negative:
            hard_pairs_for_anchor = [pair for pair in self.hard_negative_pairs 
                                   if anchor_cat in pair]
            if hard_pairs_for_anchor:
                pair = random.choice(hard_pairs_for_anchor)
                negative_cat = pair[1] if pair[0] == anchor_cat else pair[0]
            else:
                negative_cat = random.choice([c for c in self.cat_ids if c != anchor_cat])
        else:
            negative_cat = random.choice([c for c in self.cat_ids if c != anchor_cat])
        
        negative_img = random.choice(self.cat_images[negative_cat])
        
        try:
            anchor = self.transform(Image.open(anchor_img).convert('RGB'))
            positive = self.transform(Image.open(positive_img).convert('RGB'))
            negative = self.transform(Image.open(negative_img).convert('RGB'))
            
            return anchor, positive, negative
        except Exception as e:
            return self.__getitem__(random.randint(0, len(self) - 1))

class StableMegaDescriptor(nn.Module):
    """稳定的MegaDescriptor - 简化架构"""
    
    def __init__(self, feature_dim=1536):
        super().__init__()
        
        # MegaDescriptor骨干网络
        self.backbone = timm.create_model(
            'hf-hub:BVRA/MegaDescriptor-T-224',
            pretrained=True,
            num_classes=0
        )
        
        # 获取骨干网络输出维度
        with torch.no_grad():
            dummy_input = torch.randn(1, 3, 224, 224)
            backbone_output = self.backbone(dummy_input)
            backbone_dim = backbone_output.shape[1]
        
        # 简化的特征增强网络 - 减少层数和复杂度
        self.feature_enhancer = nn.Sequential(
            # 第一阶段：扩展
            nn.Linear(backbone_dim, feature_dim * 2),
            nn.BatchNorm1d(feature_dim * 2),
            nn.ReLU(inplace=True),
            nn.Dropout(0.1),  # 减少dropout
            
            # 第二阶段：精炼
            nn.Linear(feature_dim * 2, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(0.05),  # 进一步减少dropout
            
            # 输出层
            nn.Linear(feature_dim, feature_dim)
        )
        
        logger.info(f"稳定MegaDescriptor初始化: {backbone_dim} -> {feature_dim}维")
    
    def forward(self, x):
        # 骨干网络特征
        backbone_features = self.backbone(x)
        
        # 特征增强
        enhanced_features = self.feature_enhancer(backbone_features)
        
        # L2归一化
        normalized_features = torch.nn.functional.normalize(enhanced_features, p=2, dim=1)
        
        return normalized_features

class StableTrainer:
    """稳定的训练器"""
    
    def __init__(self, split_file: str = 'dataset_split.json'):
        self.split_file = split_file
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # 数据集和数据加载器
        self.dataset = StableTripletDataset(split_file, use_train_set=True)
        self.dataloader = DataLoader(
            self.dataset, 
            batch_size=8,  # 减少批次大小
            shuffle=True, 
            num_workers=2,  # 减少worker数量
            pin_memory=True
        )
        
        # 模型
        self.model = StableMegaDescriptor(feature_dim=1536).to(self.device)
        
        # 简化的损失函数 - 只使用三元组损失
        self.criterion = nn.TripletMarginLoss(margin=0.5)
        
        # 保守的优化器设置
        self.optimizer = optim.AdamW([
            {'params': self.model.backbone.parameters(), 'lr': 5e-7},  # 更低的骨干网络学习率
            {'params': self.model.feature_enhancer.parameters(), 'lr': 1e-4}  # 降低新层学习率
        ], weight_decay=1e-4)
        
        # 更平滑的学习率调度
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer, T_max=15, eta_min=1e-8
        )
        
        logger.info(f"稳定训练器初始化完成: {len(self.dataset.cat_ids)} 只猫咪")
        logger.info(f"批次数/轮: {len(self.dataloader)}")
    
    def train_epoch(self, epoch: int):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        running_loss = 0.0
        
        for batch_idx, (anchor, positive, negative) in enumerate(self.dataloader):
            anchor = anchor.to(self.device)
            positive = positive.to(self.device)
            negative = negative.to(self.device)
            
            # 前向传播
            anchor_feat = self.model(anchor)
            positive_feat = self.model(positive)
            negative_feat = self.model(negative)
            
            # 计算损失 - 只使用三元组损失
            loss = self.criterion(anchor_feat, positive_feat, negative_feat)
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪 - 更保守
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=0.5)
            
            self.optimizer.step()
            
            total_loss += loss.item()
            running_loss += loss.item()
            num_batches += 1
            
            # 更频繁的日志输出，监控稳定性
            if batch_idx % 50 == 0:
                avg_loss = running_loss / 50 if batch_idx > 0 else loss.item()
                lr = self.optimizer.param_groups[1]['lr']
                logger.info(f"Epoch {epoch}, Batch {batch_idx}: Loss={loss.item():.4f}, "
                          f"Avg50={avg_loss:.4f}, LR={lr:.2e}")
                running_loss = 0.0
        
        # 更新学习率
        self.scheduler.step()
        
        return total_loss / num_batches if num_batches > 0 else 0.0
    
    def evaluate_separability(self):
        """评估特征分离度"""
        self.model.eval()
        
        eval_cats = random.sample(self.dataset.cat_ids, min(30, len(self.dataset.cat_ids)))
        intra_dists = []
        inter_dists = []
        
        with torch.no_grad():
            for cat_id in eval_cats:
                images = self.dataset.cat_images[cat_id]
                
                # 类内距离
                if len(images) >= 2:
                    img1, img2 = random.sample(images, 2)
                    
                    tensor1 = self.dataset.transform(Image.open(img1).convert('RGB')).unsqueeze(0).to(self.device)
                    tensor2 = self.dataset.transform(Image.open(img2).convert('RGB')).unsqueeze(0).to(self.device)
                    
                    feat1 = self.model(tensor1)
                    feat2 = self.model(tensor2)
                    
                    intra_dist = torch.norm(feat1 - feat2, p=2).item()
                    intra_dists.append(intra_dist)
                
                # 类间距离
                other_cat = random.choice([c for c in eval_cats if c != cat_id])
                other_img = random.choice(self.dataset.cat_images[other_cat])
                
                tensor1 = self.dataset.transform(Image.open(random.choice(images)).convert('RGB')).unsqueeze(0).to(self.device)
                tensor2 = self.dataset.transform(Image.open(other_img).convert('RGB')).unsqueeze(0).to(self.device)
                
                feat1 = self.model(tensor1)
                feat2 = self.model(tensor2)
                
                inter_dist = torch.norm(feat1 - feat2, p=2).item()
                inter_dists.append(inter_dist)
        
        avg_intra = np.mean(intra_dists) if intra_dists else 0.0
        avg_inter = np.mean(inter_dists) if inter_dists else 0.0
        separability = avg_inter / (avg_intra + 1e-8)
        
        return {
            'intra_distance': avg_intra,
            'inter_distance': avg_inter,
            'separability': separability
        }
    
    def train(self, epochs: int = 8, save_path: str = 'stable_optimized_megadescriptor_v2.pth'):
        """完整训练流程 - 减少训练轮数"""
        logger.info(f"开始稳定训练: {epochs} epochs, {len(self.dataset.cat_ids)} 只猫咪")
        
        best_separability = 0.0
        training_history = []
        
        for epoch in range(epochs):
            start_time = time.time()
            
            # 训练
            avg_loss = self.train_epoch(epoch + 1)
            
            # 评估
            eval_stats = self.evaluate_separability()
            
            epoch_time = time.time() - start_time
            
            # 记录历史
            epoch_result = {
                'epoch': epoch + 1,
                'avg_loss': avg_loss,
                'eval_stats': eval_stats,
                'time': epoch_time
            }
            training_history.append(epoch_result)
            
            logger.info(f"Epoch {epoch + 1}/{epochs}:")
            logger.info(f"  平均损失: {avg_loss:.4f}")
            logger.info(f"  分离度: {eval_stats['separability']:.4f}")
            logger.info(f"  类内距离: {eval_stats['intra_distance']:.4f}")
            logger.info(f"  类间距离: {eval_stats['inter_distance']:.4f}")
            logger.info(f"  用时: {epoch_time:.1f}s")
            
            # 保存最佳模型
            if eval_stats['separability'] > best_separability:
                best_separability = eval_stats['separability']
                self.save_model(save_path)
                logger.info(f"🎉 保存最佳模型: 分离度 {best_separability:.4f}")
        
        logger.info(f"🚀 稳定训练完成! 最佳分离度: {best_separability:.4f}")
        
        # 保存训练历史
        history_path = save_path.replace('.pth', '_history.json')
        with open(history_path, 'w') as f:
            json.dump(training_history, f, indent=2, default=str)
        
        return best_separability, training_history
    
    def save_model(self, save_path: str):
        """保存模型"""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'feature_dim': 1536,
            'model_type': 'StableMegaDescriptor',
            'split_file': self.split_file
        }, save_path)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='稳定优化训练 V2')
    parser.add_argument('--split-file', type=str, default='dataset_split.json',
                       help='数据集分割文件')
    parser.add_argument('--epochs', type=int, default=8,
                       help='训练轮数')
    parser.add_argument('--output', type=str, default='stable_optimized_megadescriptor_v2.pth',
                       help='模型保存路径')
    
    args = parser.parse_args()
    
    # 创建训练器
    trainer = StableTrainer(split_file=args.split_file)
    
    # 开始训练
    best_sep, history = trainer.train(epochs=args.epochs, save_path=args.output)
    
    logger.info(f"🎉 稳定训练完成! 最佳分离度: {best_sep:.4f}")
    logger.info(f"📁 模型已保存: {args.output}")

if __name__ == "__main__":
    main()
