#!/usr/bin/env python3
"""
最终性能测试 - 对比优化前后的性能
"""

import os
import sys
import time
import random
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple
import logging
import argparse
import json

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from enhanced_recognizer import create_enhanced_recognizer
from optimized_recognizer import create_optimized_recognizer

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_performance_comparison(dataset_path: str, model_path: str, 
                             scales: List[int] = [5, 10, 20], rounds: int = 3):
    """运行性能对比测试"""
    dataset_path = Path(dataset_path)
    
    # 获取可用猫咪
    available_cats = []
    for cat_folder in dataset_path.iterdir():
        if not cat_folder.is_dir() or not cat_folder.name.isdigit():
            continue
        
        images = []
        for ext in ['*.jpg', '*.JPG', '*.jpeg', '*.JPEG', '*.png', '*.PNG']:
            images.extend(list(cat_folder.glob(ext)))
        
        if len(images) >= 5:
            available_cats.append((cat_folder.name, [str(img) for img in images]))
    
    available_cats.sort(key=lambda x: len(x[1]), reverse=True)
    
    print("🚀 最终性能对比测试")
    print("=" * 80)
    print(f"可用猫咪数量: {len(available_cats)}")
    print(f"测试规模: {scales}")
    print(f"每个规模轮数: {rounds}")
    
    results = {
        'baseline': {},
        'optimized': {}
    }
    
    for scale in scales:
        if scale > len(available_cats):
            print(f"⚠️ 跳过规模 {scale}: 可用数据不足")
            continue
        
        print(f"\n🎯 测试规模: {scale} 只猫咪")
        print("=" * 50)
        
        # 基线测试
        print("📊 基线模型测试")
        baseline_results = []
        
        for round_num in range(rounds):
            print(f"  基线第 {round_num + 1} 轮...")
            
            recognizer = create_enhanced_recognizer(model_path, device='cpu')
            selected_cats = random.sample(available_cats, scale)
            
            # 注册和测试
            registered_cats = []
            for cat_id, image_paths in selected_cats:
                train_count = max(3, int(len(image_paths) * 0.7))
                train_images = image_paths[:train_count]
                test_images = image_paths[train_count:]
                
                result = recognizer.register_cat(cat_id, f"Cat_{cat_id}", train_images)
                if result['success'] and test_images:
                    registered_cats.append((cat_id, test_images))
            
            # 识别测试
            correct = 0
            total = 0
            confidences = []
            similarities = []
            
            for cat_id, test_images in registered_cats:
                test_image = random.choice(test_images)
                result = recognizer.recognize_cat(test_image)
                
                total += 1
                is_correct = result.get('success') and result.get('cat_id') == cat_id
                
                if is_correct:
                    correct += 1
                    confidences.append(result.get('confidence', 0.0))
                
                similarities.append(result.get('similarity', 0.0))
            
            accuracy = correct / total if total > 0 else 0.0
            avg_confidence = np.mean(confidences) if confidences else 0.0
            avg_similarity = np.mean(similarities) if similarities else 0.0
            
            baseline_results.append({
                'accuracy': accuracy,
                'avg_confidence': avg_confidence,
                'avg_similarity': avg_similarity,
                'correct': correct,
                'total': total
            })
            
            print(f"    准确率: {accuracy:.1%}, 置信度: {avg_confidence:.1%}, 相似度: {avg_similarity:.3f}")
        
        # 优化模型测试
        print("📊 优化模型测试")
        optimized_results = []
        
        for round_num in range(rounds):
            print(f"  优化第 {round_num + 1} 轮...")
            
            recognizer = create_optimized_recognizer(model_path, device='cpu')
            selected_cats = random.sample(available_cats, scale)
            
            # 注册和测试
            registered_cats = []
            for cat_id, image_paths in selected_cats:
                train_count = max(3, int(len(image_paths) * 0.7))
                train_images = image_paths[:train_count]
                test_images = image_paths[train_count:]
                
                result = recognizer.register_cat(cat_id, f"Cat_{cat_id}", train_images)
                if result['success'] and test_images:
                    registered_cats.append((cat_id, test_images))
            
            # 识别测试
            correct = 0
            total = 0
            confidences = []
            similarities = []
            
            for cat_id, test_images in registered_cats:
                test_image = random.choice(test_images)
                result = recognizer.recognize_cat(test_image)
                
                total += 1
                is_correct = result.get('success') and result.get('cat_id') == cat_id
                
                if is_correct:
                    correct += 1
                    confidences.append(result.get('confidence', 0.0))
                
                similarities.append(result.get('similarity', 0.0))
            
            accuracy = correct / total if total > 0 else 0.0
            avg_confidence = np.mean(confidences) if confidences else 0.0
            avg_similarity = np.mean(similarities) if similarities else 0.0
            
            optimized_results.append({
                'accuracy': accuracy,
                'avg_confidence': avg_confidence,
                'avg_similarity': avg_similarity,
                'correct': correct,
                'total': total
            })
            
            print(f"    准确率: {accuracy:.1%}, 置信度: {avg_confidence:.1%}, 相似度: {avg_similarity:.3f}")
        
        # 计算统计
        baseline_accuracies = [r['accuracy'] for r in baseline_results]
        optimized_accuracies = [r['accuracy'] for r in optimized_results]
        
        results['baseline'][scale] = {
            'avg_accuracy': np.mean(baseline_accuracies),
            'accuracy_std': np.std(baseline_accuracies),
            'avg_confidence': np.mean([r['avg_confidence'] for r in baseline_results]),
            'avg_similarity': np.mean([r['avg_similarity'] for r in baseline_results]),
            'rounds': baseline_results
        }
        
        results['optimized'][scale] = {
            'avg_accuracy': np.mean(optimized_accuracies),
            'accuracy_std': np.std(optimized_accuracies),
            'avg_confidence': np.mean([r['avg_confidence'] for r in optimized_results]),
            'avg_similarity': np.mean([r['avg_similarity'] for r in optimized_results]),
            'rounds': optimized_results
        }
        
        # 显示对比
        baseline_avg = results['baseline'][scale]['avg_accuracy']
        optimized_avg = results['optimized'][scale]['avg_accuracy']
        improvement = optimized_avg - baseline_avg
        
        print(f"\n📈 规模 {scale} 对比结果:")
        print(f"   基线模型: {baseline_avg:.1%}")
        print(f"   优化模型: {optimized_avg:.1%}")
        print(f"   性能变化: {improvement:+.1%}")
        
        if improvement > 0:
            print(f"   ✅ 优化有效")
        elif improvement > -0.05:
            print(f"   ⚖️ 性能相当")
        else:
            print(f"   ⚠️ 需要调整")
    
    return results

def display_final_summary(results: Dict):
    """显示最终总结"""
    print("\n" + "=" * 80)
    print("🎯 最终性能总结")
    print("=" * 80)
    
    baseline_results = results['baseline']
    optimized_results = results['optimized']
    
    print(f"{'规模':<6} {'基线准确率':<12} {'优化准确率':<12} {'性能变化':<10} {'基线相似度':<12} {'优化相似度':<12}")
    print("-" * 80)
    
    total_improvement = 0
    valid_scales = 0
    
    for scale in sorted(baseline_results.keys()):
        if scale in optimized_results:
            baseline_acc = baseline_results[scale]['avg_accuracy']
            optimized_acc = optimized_results[scale]['avg_accuracy']
            improvement = optimized_acc - baseline_acc
            
            baseline_sim = baseline_results[scale]['avg_similarity']
            optimized_sim = optimized_results[scale]['avg_similarity']
            
            print(f"{scale:<6} {baseline_acc:<12.1%} {optimized_acc:<12.1%} "
                  f"{improvement:<10.1%} {baseline_sim:<12.3f} {optimized_sim:<12.3f}")
            
            total_improvement += improvement
            valid_scales += 1
    
    avg_improvement = total_improvement / valid_scales if valid_scales > 0 else 0
    
    print(f"\n🎯 总体评价:")
    print(f"   平均性能变化: {avg_improvement:+.1%}")
    
    if avg_improvement > 0.05:
        rating = "🌟 优化成功 - 显著提升"
    elif avg_improvement > 0.02:
        rating = "✅ 优化有效 - 适度提升"
    elif avg_improvement > -0.02:
        rating = "⚖️ 性能相当 - 需要进一步调整"
    else:
        rating = "⚠️ 优化不足 - 需要重新评估策略"
    
    print(f"   优化效果: {rating}")
    
    # 目标达成情况
    print(f"\n🎯 目标达成情况:")
    target_95_achieved = 0
    target_90_achieved = 0
    
    for scale in sorted(optimized_results.keys()):
        acc = optimized_results[scale]['avg_accuracy']
        if acc >= 0.95:
            target_95_achieved += 1
        if acc >= 0.90:
            target_90_achieved += 1
    
    print(f"   达到95%+准确率的规模: {target_95_achieved}/{len(optimized_results)}")
    print(f"   达到90%+准确率的规模: {target_90_achieved}/{len(optimized_results)}")
    
    if target_95_achieved == len(optimized_results):
        print(f"   🎉 完全达成95%+目标!")
    elif target_90_achieved == len(optimized_results):
        print(f"   ✅ 全部达成90%+目标")
    elif target_90_achieved > len(optimized_results) // 2:
        print(f"   📈 大部分达成90%+目标")
    else:
        print(f"   ⚠️ 距离目标还有差距")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='最终性能测试')
    parser.add_argument('--dataset', type=str, 
                       default='/home/<USER>/animsi/caby_training/dataset/cat_individual_images',
                       help='数据集路径')
    parser.add_argument('--model', type=str, 
                       default='training/enhanced_megadescriptor_50cats.pth',
                       help='模型路径')
    parser.add_argument('--scales', type=int, nargs='+', default=[5, 10, 20],
                       help='测试规模列表')
    parser.add_argument('--rounds', type=int, default=3,
                       help='每个规模的测试轮数')
    parser.add_argument('--output', type=str, default='final_performance_results.json',
                       help='结果保存路径')
    
    args = parser.parse_args()
    
    # 运行对比测试
    results = run_performance_comparison(args.dataset, args.model, args.scales, args.rounds)
    
    # 显示总结
    display_final_summary(results)
    
    # 保存结果
    with open(args.output, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 最终测试结果已保存: {args.output}")

if __name__ == "__main__":
    main()
