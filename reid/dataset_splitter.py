#!/usr/bin/env python3
"""
数据集分割器 - 创建独立的训练/测试集，避免过拟合
"""

import os
import sys
import random
import json
from pathlib import Path
import logging
from typing import Dict, List, Tuple

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatasetSplitter:
    """数据集分割器"""
    
    def __init__(self, dataset_path: str, train_ratio: float = 0.6, 
                 min_images_per_cat: int = 5, random_seed: int = 42):
        self.dataset_path = Path(dataset_path)
        self.train_ratio = train_ratio
        self.min_images_per_cat = min_images_per_cat
        self.random_seed = random_seed
        
        # 设置随机种子确保可重现
        random.seed(random_seed)
        
        logger.info(f"数据集分割器初始化: 训练比例={train_ratio}, 最小图片数={min_images_per_cat}")
    
    def analyze_dataset(self) -> Dict:
        """分析数据集"""
        logger.info("🔍 分析数据集...")
        
        all_cats = []
        total_images = 0
        
        for cat_folder in self.dataset_path.iterdir():
            if not cat_folder.is_dir() or not cat_folder.name.isdigit():
                continue
            
            images = []
            for ext in ['*.jpg', '*.JPG', '*.jpeg', '*.JPEG', '*.png', '*.PNG']:
                images.extend(list(cat_folder.glob(ext)))
            
            if len(images) >= self.min_images_per_cat:
                all_cats.append({
                    'cat_id': cat_folder.name,
                    'image_count': len(images),
                    'images': [str(img) for img in images]
                })
                total_images += len(images)
        
        # 按图片数量排序
        all_cats.sort(key=lambda x: x['image_count'], reverse=True)
        
        analysis = {
            'total_cats': len(all_cats),
            'total_images': total_images,
            'avg_images_per_cat': total_images / len(all_cats) if all_cats else 0,
            'min_images': min(cat['image_count'] for cat in all_cats) if all_cats else 0,
            'max_images': max(cat['image_count'] for cat in all_cats) if all_cats else 0,
            'cats_data': all_cats
        }
        
        logger.info(f"📊 数据集分析结果:")
        logger.info(f"   总猫咪数: {analysis['total_cats']}")
        logger.info(f"   总图片数: {analysis['total_images']}")
        logger.info(f"   平均图片/猫: {analysis['avg_images_per_cat']:.1f}")
        logger.info(f"   图片数范围: {analysis['min_images']} - {analysis['max_images']}")
        
        return analysis
    
    def create_train_test_split(self, train_cats_count: int = 200) -> Dict:
        """创建训练/测试集分割"""
        logger.info(f"🔄 创建训练/测试集分割 (训练集: {train_cats_count} 只猫)")
        
        # 分析数据集
        analysis = self.analyze_dataset()
        all_cats = analysis['cats_data']
        
        if len(all_cats) < train_cats_count:
            logger.warning(f"可用猫咪数量不足: 需要{train_cats_count}只，实际{len(all_cats)}只")
            train_cats_count = len(all_cats) - 50  # 保留50只作为测试
        
        # 随机打乱
        random.shuffle(all_cats)
        
        # 分割训练集和测试集
        train_cats = all_cats[:train_cats_count]
        test_cats = all_cats[train_cats_count:]
        
        # 统计信息
        train_images = sum(cat['image_count'] for cat in train_cats)
        test_images = sum(cat['image_count'] for cat in test_cats)
        
        split_info = {
            'train_cats': len(train_cats),
            'test_cats': len(test_cats),
            'train_images': train_images,
            'test_images': test_images,
            'train_cat_ids': [cat['cat_id'] for cat in train_cats],
            'test_cat_ids': [cat['cat_id'] for cat in test_cats],
            'train_data': train_cats,
            'test_data': test_cats,
            'random_seed': self.random_seed
        }
        
        logger.info(f"✅ 数据集分割完成:")
        logger.info(f"   训练集: {len(train_cats)} 只猫, {train_images} 张图片")
        logger.info(f"   测试集: {len(test_cats)} 只猫, {test_images} 张图片")
        logger.info(f"   训练/测试比例: {len(train_cats)/(len(train_cats)+len(test_cats)):.1%}")
        
        return split_info
    
    def save_split_info(self, split_info: Dict, output_path: str = 'dataset_split.json'):
        """保存分割信息"""
        with open(output_path, 'w') as f:
            json.dump(split_info, f, indent=2)
        
        logger.info(f"💾 数据集分割信息已保存: {output_path}")
        
        # 同时保存简化版本用于快速查看
        summary = {
            'train_cats': split_info['train_cats'],
            'test_cats': split_info['test_cats'],
            'train_images': split_info['train_images'],
            'test_images': split_info['test_images'],
            'train_cat_ids': split_info['train_cat_ids'][:20],  # 只显示前20个
            'test_cat_ids': split_info['test_cat_ids'][:20],
            'random_seed': split_info['random_seed']
        }
        
        summary_path = output_path.replace('.json', '_summary.json')
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)
        
        logger.info(f"📋 数据集分割摘要已保存: {summary_path}")
    
    def validate_split(self, split_info: Dict):
        """验证分割的独立性"""
        logger.info("🔍 验证训练/测试集独立性...")
        
        train_ids = set(split_info['train_cat_ids'])
        test_ids = set(split_info['test_cat_ids'])
        
        # 检查重叠
        overlap = train_ids.intersection(test_ids)
        
        if overlap:
            logger.error(f"❌ 发现重叠猫咪: {overlap}")
            return False
        else:
            logger.info(f"✅ 训练/测试集完全独立，无重叠")
            return True
    
    def display_split_statistics(self, split_info: Dict):
        """显示分割统计信息"""
        print("\n" + "=" * 80)
        print("📊 数据集分割统计")
        print("=" * 80)
        
        train_data = split_info['train_data']
        test_data = split_info['test_data']
        
        # 训练集统计
        train_image_counts = [cat['image_count'] for cat in train_data]
        print(f"🎯 训练集:")
        print(f"   猫咪数量: {len(train_data)}")
        print(f"   图片总数: {sum(train_image_counts)}")
        print(f"   平均图片/猫: {sum(train_image_counts)/len(train_image_counts):.1f}")
        print(f"   图片数范围: {min(train_image_counts)} - {max(train_image_counts)}")
        
        # 测试集统计
        test_image_counts = [cat['image_count'] for cat in test_data]
        print(f"\n🔍 测试集:")
        print(f"   猫咪数量: {len(test_data)}")
        print(f"   图片总数: {sum(test_image_counts)}")
        print(f"   平均图片/猫: {sum(test_image_counts)/len(test_image_counts):.1f}")
        print(f"   图片数范围: {min(test_image_counts)} - {max(test_image_counts)}")
        
        # 显示部分ID
        print(f"\n📋 训练集猫咪ID (前20个): {split_info['train_cat_ids'][:20]}")
        print(f"📋 测试集猫咪ID (前20个): {split_info['test_cat_ids'][:20]}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='数据集分割器')
    parser.add_argument('--dataset', type=str, 
                       default='../dataset/cat_individual_images',
                       help='数据集路径')
    parser.add_argument('--train-cats', type=int, default=200,
                       help='训练集猫咪数量')
    parser.add_argument('--min-images', type=int, default=5,
                       help='每只猫最少图片数')
    parser.add_argument('--seed', type=int, default=42,
                       help='随机种子')
    parser.add_argument('--output', type=str, default='dataset_split.json',
                       help='输出文件路径')
    
    args = parser.parse_args()
    
    # 创建分割器
    splitter = DatasetSplitter(
        dataset_path=args.dataset,
        min_images_per_cat=args.min_images,
        random_seed=args.seed
    )
    
    # 创建分割
    split_info = splitter.create_train_test_split(train_cats_count=args.train_cats)
    
    # 验证独立性
    is_valid = splitter.validate_split(split_info)
    
    if is_valid:
        # 保存分割信息
        splitter.save_split_info(split_info, args.output)
        
        # 显示统计
        splitter.display_split_statistics(split_info)
        
        print(f"\n🎉 数据集分割成功完成!")
        print(f"   训练集: {split_info['train_cats']} 只猫咪")
        print(f"   测试集: {split_info['test_cats']} 只猫咪")
        print(f"   完全独立，无重叠")
    else:
        print(f"\n❌ 数据集分割失败: 存在重叠")

if __name__ == "__main__":
    main()
