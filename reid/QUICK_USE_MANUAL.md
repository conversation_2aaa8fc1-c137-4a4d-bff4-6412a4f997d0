# 🚀 猫咪识别系统 V2 - 快速使用手册

## 📊 系统性能对比

### V2 优化版本 (最新)
- **特征维度**: 1536维
- **训练策略**: 困难负样本挖掘 + 置信度校准
- **识别策略**: 多尺度特征融合 + 自适应阈值
- **预期准确率**: 90%+ (目标95%+)
- **状态**: 🧪 实验中

### V1 稳定版本 (已验证)
- **准确率**: 86.7% ± 11.9%
- **特征维度**: 1024维
- **训练方法**: 三元组损失 + 自适应阈值
- **状态**: ✅ 生产就绪

## 🎯 快速开始

### 1. 数据准备 (如果还没有)
```bash
# 分割数据集
python dataset_splitter.py --input ../dataset/cat_individual_images --output dataset_split.json
```

### 2. V2 优化训练 (推荐)
```bash
# 优化训练 - 1536维特征 + 置信度校准
python optimized_training_v2.py --epochs 10 --output optimized_megadescriptor_v2.pth
```

### 3. 快速测试所有模型
```bash
# 测试所有可用模型
python quick_test_v2.py --all

# 测试特定模型
python quick_test_v2.py --model optimized_megadescriptor_v2.pth --strategy adaptive --cats 20
```

### 4. V2 优化识别器使用

```python
from optimized_recognizer_v2 import OptimizedCatRecognizer

# 创建识别器 (支持多种策略)
recognizer = OptimizedCatRecognizer(
    'optimized_megadescriptor_v2.pth',
    strategy='adaptive'  # conservative, balanced, aggressive, adaptive
)

# 注册猫咪 (至少3张图片)
result = recognizer.register_cat(
    cat_id='001',
    cat_name='Fluffy',
    image_paths=['cat1_img1.jpg', 'cat1_img2.jpg', 'cat1_img3.jpg']
)

# 识别猫咪 (返回详细信息)
result = recognizer.recognize_cat('unknown_cat.jpg')
print(f"识别结果: {result}")
# 包含: confidence, similarity, stability, threshold_used, strategy_used
```

### 5. V1 稳定版本使用

```python
from adaptive_recognizer import AdaptiveCatRecognizer

# 创建识别器
recognizer = AdaptiveCatRecognizer('fast_megadescriptor_100cats_best.pth')

# 注册和识别
recognizer.register_cat('001', 'Fluffy', ['cat1_img1.jpg', 'cat1_img2.jpg'])
result = recognizer.recognize_cat('unknown_cat.jpg')
```

## ⚙️ 配置说明

### 高精度配置（推荐）
```python
config = {
    'feature_dim': 1536,           # MegaDescriptor-L-384特征维度
    'max_cats': 50,                # 最大支持猫咪数
    'confidence_threshold': 0.60,  # 识别阈值（降低提高召回率）
    'feature_db_path': 'cat_db'    # 数据库路径
}
```

### 保守配置（高精度要求）
```python
config = {
    'feature_dim': 1536,
    'max_cats': 20,
    'confidence_threshold': 0.75,  # 提高阈值确保精度
    'feature_db_path': 'cat_db'
}
```

## 📈 性能优化建议

### 注册阶段
1. **使用8张高质量图片**
   - 不同角度：正面、侧面、45度角
   - 不同光照：自然光、室内光
   - 清晰度：避免模糊、遮挡

2. **图片质量要求**
   - 分辨率：建议384x384或更高
   - 格式：JPG、PNG
   - 猫咪占比：建议占图片50%以上

### 识别阶段
1. **输入图片建议**
   - 单只猫咪，避免多猫同框
   - 清晰的正面或侧面照
   - 良好的光照条件

2. **阈值调整**
   - 高精度需求：threshold = 0.75
   - 高召回需求：threshold = 0.60
   - 平衡设置：threshold = 0.70

## 🔧 故障排除

### 常见问题

**Q: 识别准确率低怎么办？**
A: 
1. 检查注册图片质量和数量
2. 降低confidence_threshold
3. 增加更多训练样本

**Q: 响应速度慢怎么办？**
A: 
1. 确保使用GPU（CUDA）
2. 减少max_cats参数
3. 优化图片预处理

**Q: 出现"Unknown"识别结果？**
A: 
1. 降低confidence_threshold
2. 检查图片质量
3. 确认猫咪已正确注册

### 错误代码

- **特征提取失败**: 检查图片路径和格式
- **注册失败**: 确保图片数量≥3张
- **数据库错误**: 检查存储路径权限

## 📊 性能监控

### 获取系统统计
```python
stats = recognizer.get_system_stats()
print(f"已注册猫咪: {stats['registered_cats']}")
print(f"总预测数: {stats['total_predictions']}")
print(f"高置信度率: {stats['high_confidence_rate']:.1%}")
```

### 性能指标
- **准确率**: >90%为优秀
- **高置信度率**: >50%为良好
- **响应时间**: <0.5秒为优秀

## 🎯 最佳实践

### 1. 数据准备
```python
# 推荐的注册流程
def register_cat_best_practice(recognizer, cat_id, cat_name, image_dir):
    # 获取所有图片
    images = list(Path(image_dir).glob('*.jpg'))
    
    # 选择最佳的8张图片（质量筛选）
    best_images = select_best_images(images, count=8)
    
    # 注册
    result = recognizer.register_cat(cat_id, cat_name, best_images)
    return result
```

### 2. 批量识别
```python
def batch_recognize(recognizer, image_paths):
    results = []
    for img_path in image_paths:
        result = recognizer.recognize_cat(img_path)
        results.append(result)
    return results
```

### 3. 结果验证
```python
def validate_result(result, min_confidence=0.70):
    if result['status'] == 'known' and result['confidence'] >= min_confidence:
        return True, result['cat_name']
    else:
        return False, "识别不确定"
```

## 📁 文件结构

```
reid/
├── training/megadescriptor_recognizer.py  # 主识别器 ⭐
├── test_megadescriptor_final.py          # 性能测试 ⭐
├── QUICK_USE_MANUAL.md                   # 本手册 ⭐
└── FINAL_SYSTEM_REPORT.md                # 详细报告
```

## 🆘 技术支持

- **详细报告**: 查看 `FINAL_SYSTEM_REPORT.md`
- **改进计划**: 查看 `IMPROVEMENT_ROADMAP.md`
- **测试结果**: 运行测试脚本查看最新性能

---

**手册版本**: v3.0  
**最后更新**: 2025-07-07  
**适用系统**: MegaDescriptor-L-384增强版
