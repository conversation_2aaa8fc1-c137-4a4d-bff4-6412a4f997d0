#!/usr/bin/env python3
"""
混合猫咪识别系统
结合专门化分类器和泛化特征提取器
既保证对原始3只猫的100%识别，又支持任意新猫咪
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
from pathlib import Path
import random
import time
import logging
import json
from typing import Dict, List, Tuple, Optional
from PIL import Image
import torchvision.transforms as transforms
from collections import defaultdict
import timm

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HybridCatModel(nn.Module):
    """混合猫咪识别模型"""
    
    def __init__(self, known_cats: int = 3, feature_dim: int = 1024):
        super().__init__()
        
        # 共享的骨干网络
        self.backbone = timm.create_model(
            'hf-hub:BVRA/MegaDescriptor-T-224',
            pretrained=True,
            num_classes=0
        )
        
        # 获取骨干网络输出维度
        with torch.no_grad():
            dummy_input = torch.randn(1, 3, 224, 224)
            backbone_output = self.backbone(dummy_input)
            backbone_dim = backbone_output.shape[1]
        
        # 专门化分支：用于已知的3只猫咪
        self.specialist_branch = nn.Sequential(
            nn.Linear(backbone_dim, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(feature_dim, feature_dim // 2),
            nn.BatchNorm1d(feature_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(feature_dim // 2, known_cats)
        )
        
        # 泛化分支：用于特征提取和新猫咪
        self.generalist_branch = nn.Sequential(
            nn.Linear(backbone_dim, feature_dim * 2),
            nn.BatchNorm1d(feature_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(feature_dim * 2, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(feature_dim, feature_dim)
        )
        
        # 置信度预测器：决定使用哪个分支
        self.confidence_predictor = nn.Sequential(
            nn.Linear(backbone_dim, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, 1),
            nn.Sigmoid()
        )
        
        self.known_cats = known_cats
        self.feature_dim = feature_dim
        
        logger.info(f"混合模型初始化: {backbone_dim} -> 专门化({known_cats}类) + 泛化({feature_dim}维)")
    
    def forward(self, x, mode='hybrid'):
        """
        前向传播
        mode: 'specialist', 'generalist', 'hybrid'
        """
        # 共享特征提取
        backbone_features = self.backbone(x)
        
        if mode == 'specialist':
            # 仅使用专门化分支
            specialist_logits = self.specialist_branch(backbone_features)
            return specialist_logits
        
        elif mode == 'generalist':
            # 仅使用泛化分支
            generalist_features = self.generalist_branch(backbone_features)
            return torch.nn.functional.normalize(generalist_features, p=2, dim=1)
        
        else:  # hybrid
            # 混合模式：同时输出两个分支的结果和置信度
            specialist_logits = self.specialist_branch(backbone_features)
            generalist_features = self.generalist_branch(backbone_features)
            generalist_features = torch.nn.functional.normalize(generalist_features, p=2, dim=1)
            confidence = self.confidence_predictor(backbone_features)
            
            return {
                'specialist_logits': specialist_logits,
                'generalist_features': generalist_features,
                'confidence': confidence
            }

class HybridTrainingDataset(Dataset):
    """混合训练数据集"""
    
    def __init__(self, annotations_file: str, images_dir: str, train: bool = True, train_ratio: float = 0.9):
        self.annotations_file = annotations_file
        self.images_dir = images_dir
        self.train = train
        
        # 加载标注数据
        with open(annotations_file, 'r', encoding='utf-8') as f:
            self.annotations = json.load(f)
        
        # 过滤出三只主要猫咪
        self.valid_categories = ['小白', '小花', '小黑']
        self.filtered_data = {}
        
        for img_name, annotation in self.annotations.items():
            category = annotation.get('category', 'unknown')
            if category in self.valid_categories:
                img_path = os.path.join(images_dir, img_name)
                if os.path.exists(img_path):
                    self.filtered_data[img_name] = {
                        'category': category,
                        'path': img_path
                    }
        
        # 按类别分组
        self.category_images = defaultdict(list)
        for img_name, data in self.filtered_data.items():
            self.category_images[data['category']].append((img_name, data['path']))
        
        # 创建类别映射
        self.category_to_idx = {cat: idx for idx, cat in enumerate(self.valid_categories)}
        self.idx_to_category = {idx: cat for cat, idx in self.category_to_idx.items()}
        
        # 分割数据 - 使用更多数据训练以达到100%
        self.data_list = []
        for category, images in self.category_images.items():
            random.shuffle(images)
            split_idx = int(len(images) * train_ratio)
            
            if train:
                selected_images = images[:split_idx]
            else:
                selected_images = images[split_idx:]
            
            for img_name, img_path in selected_images:
                self.data_list.append({
                    'image_path': img_path,
                    'category': category,
                    'label': self.category_to_idx[category]
                })
        
        # 强化数据增强以提高泛化能力
        if train:
            self.transform = transforms.Compose([
                transforms.Resize((256, 256)),
                transforms.RandomCrop((224, 224)),
                transforms.RandomHorizontalFlip(p=0.5),
                transforms.RandomRotation(degrees=20),
                transforms.ColorJitter(brightness=0.3, contrast=0.3, saturation=0.3, hue=0.15),
                transforms.RandomGrayscale(p=0.1),
                transforms.RandomApply([transforms.GaussianBlur(3, sigma=(0.1, 2.0))], p=0.1),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                                   std=[0.229, 0.224, 0.225])
            ])
        else:
            self.transform = transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                                   std=[0.229, 0.224, 0.225])
            ])
        
        logger.info(f"{'训练' if train else '验证'}数据集: {len(self.data_list)} 张图片")
        for category in self.valid_categories:
            count = sum(1 for item in self.data_list if item['category'] == category)
            logger.info(f"  {category}: {count} 张")
    
    def __len__(self):
        return len(self.data_list)
    
    def __getitem__(self, idx):
        item = self.data_list[idx]
        
        # 加载图像
        image = Image.open(item['image_path']).convert('RGB')
        image = self.transform(image)
        
        return image, item['label']

class HybridTrainer:
    """混合模型训练器"""
    
    def __init__(self, annotations_file: str, images_dir: str):
        self.annotations_file = annotations_file
        self.images_dir = images_dir
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # 创建数据集 - 使用90%数据训练以获得更好性能
        self.train_dataset = HybridTrainingDataset(annotations_file, images_dir, train=True, train_ratio=0.9)
        self.val_dataset = HybridTrainingDataset(annotations_file, images_dir, train=False, train_ratio=0.9)
        
        # 创建数据加载器
        self.train_loader = DataLoader(
            self.train_dataset, 
            batch_size=20,  # 增大批次大小
            shuffle=True, 
            num_workers=4,
            pin_memory=True
        )
        
        self.val_loader = DataLoader(
            self.val_dataset, 
            batch_size=20, 
            shuffle=False, 
            num_workers=4,
            pin_memory=True
        )
        
        # 创建模型
        self.model = HybridCatModel(known_cats=3, feature_dim=1024).to(self.device)
        
        # 损失函数
        self.specialist_criterion = nn.CrossEntropyLoss(label_smoothing=0.05)  # 减少标签平滑
        self.generalist_criterion = nn.TripletMarginLoss(margin=0.5)
        
        # 优化器 - 使用更小的学习率
        self.optimizer = optim.AdamW([
            {'params': self.model.backbone.parameters(), 'lr': 5e-6},  # 极小的骨干网络学习率
            {'params': self.model.specialist_branch.parameters(), 'lr': 1e-4},
            {'params': self.model.generalist_branch.parameters(), 'lr': 5e-5},
            {'params': self.model.confidence_predictor.parameters(), 'lr': 1e-4}
        ], weight_decay=1e-5)
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer, T_max=100, eta_min=1e-8
        )
        
        self.training_history = []
        
        logger.info(f"混合训练器初始化完成")
        logger.info(f"训练样本: {len(self.train_dataset)}")
        logger.info(f"验证样本: {len(self.val_dataset)}")
    
    def train_epoch(self, epoch: int):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        specialist_correct = 0
        total = 0
        
        for batch_idx, (images, labels) in enumerate(self.train_loader):
            images = images.to(self.device)
            labels = labels.to(self.device)
            
            # 前向传播 - 专门化分支
            specialist_logits = self.model(images, mode='specialist')
            specialist_loss = self.specialist_criterion(specialist_logits, labels)
            
            # 生成三元组用于泛化分支训练
            generalist_features = self.model(images, mode='generalist')
            
            # 简化的三元组损失（在批次内构建）
            triplet_loss = 0.0
            if len(set(labels.cpu().numpy())) > 1:  # 确保批次中有不同类别
                # 为每个样本找到正负样本
                for i in range(len(labels)):
                    anchor_label = labels[i]
                    anchor_feature = generalist_features[i]
                    
                    # 找正样本
                    positive_indices = (labels == anchor_label).nonzero(as_tuple=True)[0]
                    if len(positive_indices) > 1:
                        positive_idx = positive_indices[positive_indices != i][0] if len(positive_indices[positive_indices != i]) > 0 else positive_indices[0]
                        positive_feature = generalist_features[positive_idx]
                        
                        # 找负样本
                        negative_indices = (labels != anchor_label).nonzero(as_tuple=True)[0]
                        if len(negative_indices) > 0:
                            negative_idx = negative_indices[0]
                            negative_feature = generalist_features[negative_idx]
                            
                            # 计算三元组损失
                            pos_dist = torch.nn.functional.pairwise_distance(anchor_feature.unsqueeze(0), positive_feature.unsqueeze(0))
                            neg_dist = torch.nn.functional.pairwise_distance(anchor_feature.unsqueeze(0), negative_feature.unsqueeze(0))
                            triplet_loss += torch.relu(pos_dist - neg_dist + 0.5).mean()
            
            # 总损失
            total_batch_loss = specialist_loss + 0.1 * triplet_loss
            
            # 反向传播
            self.optimizer.zero_grad()
            total_batch_loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=0.5)
            
            self.optimizer.step()
            
            # 统计
            total_loss += total_batch_loss.item()
            _, predicted = specialist_logits.max(1)
            total += labels.size(0)
            specialist_correct += predicted.eq(labels).sum().item()
            
            if batch_idx % 20 == 0:
                logger.info(f'Epoch {epoch}, Batch {batch_idx}: Loss={total_batch_loss.item():.4f}, '
                          f'Acc={100.*specialist_correct/total:.2f}%')
        
        # 更新学习率
        self.scheduler.step()
        
        avg_loss = total_loss / len(self.train_loader)
        accuracy = 100. * specialist_correct / total
        
        return avg_loss, accuracy
    
    def validate(self):
        """验证模型"""
        self.model.eval()
        total_loss = 0.0
        correct = 0
        total = 0
        class_correct = defaultdict(int)
        class_total = defaultdict(int)
        
        with torch.no_grad():
            for images, labels in self.val_loader:
                images = images.to(self.device)
                labels = labels.to(self.device)
                
                # 使用专门化分支进行验证
                specialist_logits = self.model(images, mode='specialist')
                loss = self.specialist_criterion(specialist_logits, labels)
                
                total_loss += loss.item()
                _, predicted = specialist_logits.max(1)
                total += labels.size(0)
                correct += predicted.eq(labels).sum().item()
                
                # 按类别统计
                for i in range(labels.size(0)):
                    label = labels[i].item()
                    class_total[label] += 1
                    if predicted[i] == labels[i]:
                        class_correct[label] += 1
        
        avg_loss = total_loss / len(self.val_loader)
        accuracy = 100. * correct / total
        
        # 计算各类别准确率
        class_accuracies = {}
        for class_idx in range(3):
            if class_total[class_idx] > 0:
                class_acc = 100. * class_correct[class_idx] / class_total[class_idx]
                category_name = self.train_dataset.idx_to_category[class_idx]
                class_accuracies[category_name] = class_acc
        
        return avg_loss, accuracy, class_accuracies
    
    def train(self, num_epochs: int = 100, save_path: str = 'hybrid_cat_model.pth'):
        """完整训练过程"""
        logger.info(f"开始混合模型训练: {num_epochs} epochs")
        
        best_accuracy = 0.0
        patience = 0
        max_patience = 15
        
        for epoch in range(num_epochs):
            start_time = time.time()
            
            # 训练
            train_loss, train_acc = self.train_epoch(epoch + 1)
            
            # 验证
            val_loss, val_acc, class_accs = self.validate()
            
            epoch_time = time.time() - start_time
            
            # 记录历史
            epoch_result = {
                'epoch': epoch + 1,
                'train_loss': train_loss,
                'train_accuracy': train_acc,
                'val_loss': val_loss,
                'val_accuracy': val_acc,
                'class_accuracies': class_accs,
                'time': epoch_time
            }
            self.training_history.append(epoch_result)
            
            logger.info(f"Epoch {epoch + 1}/{num_epochs}:")
            logger.info(f"  训练: Loss={train_loss:.4f}, Acc={train_acc:.2f}%")
            logger.info(f"  验证: Loss={val_loss:.4f}, Acc={val_acc:.2f}%")
            logger.info(f"  各类别准确率: {class_accs}")
            logger.info(f"  用时: {epoch_time:.1f}s")
            
            # 保存最佳模型
            if val_acc > best_accuracy:
                best_accuracy = val_acc
                self.save_model(save_path)
                logger.info(f"🎉 保存最佳模型: 验证准确率 {best_accuracy:.2f}%")
                patience = 0
            else:
                patience += 1
            
            # 早停
            if patience >= max_patience:
                logger.info(f"🛑 早停: 验证准确率已达到 {best_accuracy:.2f}%")
                break
            
            # 如果准确率达到100%，可以提前结束
            if val_acc >= 100.0:
                logger.info(f"🎯 达到完美准确率: {val_acc:.2f}%")
                break
        
        logger.info(f"🚀 混合模型训练完成! 最佳验证准确率: {best_accuracy:.2f}%")
        
        # 保存训练历史
        history_path = save_path.replace('.pth', '_history.json')
        with open(history_path, 'w') as f:
            json.dump(self.training_history, f, indent=2, default=str)
        
        return best_accuracy
    
    def save_model(self, save_path: str):
        """保存模型"""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'feature_dim': self.model.feature_dim,
            'known_cats': self.model.known_cats,
            'model_type': 'HybridCatModel',
            'category_to_idx': self.train_dataset.category_to_idx,
            'idx_to_category': self.train_dataset.idx_to_category,
            'training_history': self.training_history
        }, save_path, _use_new_zipfile_serialization=False)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='混合猫咪识别系统训练')
    parser.add_argument('--annotations', type=str, 
                       default='/home/<USER>/animsi/caby_training/tagging/annotations.json',
                       help='标注文件路径')
    parser.add_argument('--images', type=str, 
                       default='/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails',
                       help='图片目录路径')
    parser.add_argument('--epochs', type=int, default=100, help='训练轮数')
    parser.add_argument('--output', type=str, default='hybrid_cat_model.pth', help='输出模型路径')
    
    args = parser.parse_args()
    
    # 检查文件存在性
    if not os.path.exists(args.annotations):
        print(f"❌ 标注文件不存在: {args.annotations}")
        return
    
    if not os.path.exists(args.images):
        print(f"❌ 图片目录不存在: {args.images}")
        return
    
    # 创建训练器
    trainer = HybridTrainer(args.annotations, args.images)
    
    # 开始训练
    best_accuracy = trainer.train(args.epochs, args.output)
    
    logger.info(f"🎉 混合猫咪识别系统训练完成! 最佳准确率: {best_accuracy:.2f}%")
    logger.info(f"📁 模型已保存: {args.output}")

if __name__ == "__main__":
    main()
