#!/usr/bin/env python3
"""
测试恢复的原始三只猫咪模型
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
from pathlib import Path
import random
import logging
import json
from typing import Dict, List, Tuple
from PIL import Image
import torchvision.transforms as transforms
from collections import defaultdict
import timm

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OriginalCatsModel(nn.Module):
    """原始三只猫咪识别模型"""
    
    def __init__(self, num_classes: int = 3, feature_dim: int = 768):
        super().__init__()
        
        # 使用MegaDescriptor作为骨干网络
        self.backbone = timm.create_model(
            'hf-hub:BVRA/MegaDescriptor-T-224',
            pretrained=True,
            num_classes=0  # 移除分类头
        )
        
        # 获取骨干网络输出维度
        with torch.no_grad():
            dummy_input = torch.randn(1, 3, 224, 224)
            backbone_output = self.backbone(dummy_input)
            backbone_dim = backbone_output.shape[1]
        
        # 特征提取器
        self.feature_extractor = nn.Sequential(
            nn.Linear(backbone_dim, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 分类头
        self.classifier = nn.Linear(feature_dim, num_classes)
        
        self.feature_dim = feature_dim
        self.num_classes = num_classes
    
    def forward(self, x, return_features=False):
        # 骨干网络特征提取
        backbone_features = self.backbone(x)
        
        # 特征增强
        features = self.feature_extractor(backbone_features)
        
        if return_features:
            return features
        
        # 分类
        logits = self.classifier(features)
        
        return logits

class OriginalCatsTester:
    """原始三只猫咪测试器"""
    
    def __init__(self, model_path: str, annotations_file: str, images_dir: str):
        self.model_path = model_path
        self.annotations_file = annotations_file
        self.images_dir = images_dir
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # 图像变换
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        # 加载模型
        self.model, self.category_mapping = self._load_model()
        
        # 加载标注数据
        with open(annotations_file, 'r', encoding='utf-8') as f:
            self.annotations = json.load(f)
        
        logger.info("原始三只猫咪测试器初始化完成")
    
    def _load_model(self):
        """加载模型"""
        checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
        
        # 创建模型
        feature_dim = checkpoint.get('feature_dim', 768)
        num_classes = checkpoint.get('num_classes', 3)
        model = OriginalCatsModel(num_classes=num_classes, feature_dim=feature_dim)
        
        # 加载权重
        model.load_state_dict(checkpoint['model_state_dict'])
        model = model.to(self.device)
        model.eval()
        
        # 获取类别映射
        idx_to_category = checkpoint.get('idx_to_category', {0: '小白', 1: '小花', 2: '小黑'})
        category_to_idx = checkpoint.get('category_to_idx', {'小白': 0, '小花': 1, '小黑': 2})
        
        logger.info(f"成功加载模型: {num_classes} 类, 特征维度: {feature_dim}")
        logger.info(f"类别映射: {idx_to_category}")
        
        return model, {'idx_to_category': idx_to_category, 'category_to_idx': category_to_idx}
    
    def predict_image(self, image_path: str) -> Tuple[str, float]:
        """预测单张图片"""
        try:
            image = Image.open(image_path).convert('RGB')
            image_tensor = self.transform(image).unsqueeze(0).to(self.device)
            
            with torch.no_grad():
                outputs = self.model(image_tensor)
                probabilities = torch.softmax(outputs, dim=1)
                confidence, predicted_idx = torch.max(probabilities, 1)
                
                predicted_idx_val = predicted_idx.item()
                predicted_category = self.category_mapping['idx_to_category'].get(str(predicted_idx_val),
                                   self.category_mapping['idx_to_category'].get(predicted_idx_val, f'unknown_{predicted_idx_val}'))
                confidence_score = confidence.item()
                
                return predicted_category, confidence_score
        except Exception as e:
            logger.error(f"预测失败 {image_path}: {e}")
            return None, 0.0
    
    def test_all_annotations(self, confidence_threshold: float = 0.5) -> Dict:
        """测试所有标注数据"""
        logger.info("开始测试所有标注数据")
        
        # 过滤出三只主要猫咪
        valid_categories = ['小白', '小花', '小黑']
        test_data = {}
        
        for img_name, annotation in self.annotations.items():
            category = annotation.get('category', 'unknown')
            if category in valid_categories:
                img_path = os.path.join(self.images_dir, img_name)
                if os.path.exists(img_path):
                    test_data[img_name] = {
                        'true_category': category,
                        'image_path': img_path
                    }
        
        logger.info(f"找到 {len(test_data)} 张有效测试图片")
        
        # 统计真实类别分布
        true_category_counts = defaultdict(int)
        for data in test_data.values():
            true_category_counts[data['true_category']] += 1
        
        logger.info("真实类别分布:")
        for category, count in sorted(true_category_counts.items()):
            logger.info(f"  {category}: {count} 张")
        
        # 开始测试
        results = {
            'total_tested': 0,
            'correct_predictions': 0,
            'high_confidence_correct': 0,
            'category_results': defaultdict(lambda: {'correct': 0, 'total': 0, 'details': []}),
            'confusion_matrix': defaultdict(lambda: defaultdict(int)),
            'detailed_results': []
        }
        
        for img_name, data in test_data.items():
            true_category = data['true_category']
            img_path = data['image_path']
            
            # 预测
            predicted_category, confidence = self.predict_image(img_path)
            
            if predicted_category is None:
                continue
            
            results['total_tested'] += 1
            results['category_results'][true_category]['total'] += 1
            
            # 判断是否正确
            is_correct = predicted_category == true_category
            is_high_confidence = confidence >= confidence_threshold
            
            if is_correct:
                results['correct_predictions'] += 1
                results['category_results'][true_category]['correct'] += 1
                
                if is_high_confidence:
                    results['high_confidence_correct'] += 1
            
            # 更新混淆矩阵
            results['confusion_matrix'][true_category][predicted_category] += 1
            
            # 记录详细结果
            detail = {
                'image_name': img_name,
                'true_category': true_category,
                'predicted_category': predicted_category,
                'confidence': confidence,
                'is_correct': is_correct,
                'is_high_confidence': is_high_confidence
            }
            
            results['category_results'][true_category]['details'].append(detail)
            results['detailed_results'].append(detail)
        
        # 计算准确率
        overall_accuracy = results['correct_predictions'] / results['total_tested'] if results['total_tested'] > 0 else 0.0
        high_conf_accuracy = results['high_confidence_correct'] / results['total_tested'] if results['total_tested'] > 0 else 0.0
        
        results['overall_accuracy'] = overall_accuracy
        results['high_confidence_accuracy'] = high_conf_accuracy
        
        # 计算各类别准确率
        for category, category_data in results['category_results'].items():
            if category_data['total'] > 0:
                category_data['accuracy'] = category_data['correct'] / category_data['total']
            else:
                category_data['accuracy'] = 0.0
        
        logger.info(f"测试完成: 总体准确率 {overall_accuracy:.1%}")
        logger.info(f"高置信度准确率: {high_conf_accuracy:.1%}")
        
        return results
    
    def generate_report(self, results: Dict, output_file: str = 'original_cats_test_report.json'):
        """生成测试报告"""
        # 保存详细结果
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        # 打印摘要报告
        print("\n" + "="*60)
        print("🔍 原始三只猫咪模型测试报告")
        print("="*60)
        
        print(f"\n📊 总体结果:")
        print(f"   总测试数量: {results['total_tested']}")
        print(f"   正确识别: {results['correct_predictions']}")
        print(f"   总体准确率: {results['overall_accuracy']:.1%}")
        print(f"   高置信度准确率: {results['high_confidence_accuracy']:.1%}")
        
        print(f"\n📋 各类别准确率:")
        for category, category_result in results['category_results'].items():
            accuracy = category_result['accuracy']
            correct = category_result['correct']
            total = category_result['total']
            print(f"   {category}: {accuracy:.1%} ({correct}/{total})")
        
        print(f"\n🔄 混淆矩阵:")
        confusion = results['confusion_matrix']
        categories = sorted(confusion.keys())
        
        # 打印表头
        header = "真实\\预测"
        print(f"   {header:<10}", end="")
        for cat in categories:
            predicted_cats = sorted(confusion[cat].keys())
            for pred_cat in predicted_cats:
                print(f"\t{pred_cat[:8]}", end="")
            break
        print()
        
        # 打印矩阵
        for true_cat in categories:
            print(f"   {true_cat[:8]}", end="")
            predicted_cats = sorted(confusion[true_cat].keys())
            for pred_cat in predicted_cats:
                count = confusion[true_cat][pred_cat]
                print(f"\t{count}", end="")
            print()
        
        # 分析置信度分布
        confidences = [detail['confidence'] for detail in results['detailed_results']]
        if confidences:
            avg_confidence = np.mean(confidences)
            std_confidence = np.std(confidences)
            print(f"\n📈 置信度分析:")
            print(f"   平均置信度: {avg_confidence:.3f} ± {std_confidence:.3f}")
            print(f"   最高置信度: {np.max(confidences):.3f}")
            print(f"   最低置信度: {np.min(confidences):.3f}")
        
        print(f"\n📁 详细报告已保存: {output_file}")
        
        return results

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='测试原始三只猫咪模型')
    parser.add_argument('--model', type=str, default='original_cats_model.pth', 
                       help='模型文件路径')
    parser.add_argument('--annotations', type=str, 
                       default='/home/<USER>/animsi/caby_training/tagging/annotations.json',
                       help='标注文件路径')
    parser.add_argument('--images', type=str, 
                       default='/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails',
                       help='图片目录路径')
    parser.add_argument('--threshold', type=float, default=0.5, help='置信度阈值')
    parser.add_argument('--output', type=str, default='original_cats_test_report.json', help='输出文件')
    
    args = parser.parse_args()
    
    # 检查文件存在性
    if not os.path.exists(args.model):
        print(f"❌ 模型文件不存在: {args.model}")
        return
    
    if not os.path.exists(args.annotations):
        print(f"❌ 标注文件不存在: {args.annotations}")
        return
    
    if not os.path.exists(args.images):
        print(f"❌ 图片目录不存在: {args.images}")
        return
    
    # 创建测试器
    tester = OriginalCatsTester(args.model, args.annotations, args.images)
    
    # 执行测试
    results = tester.test_all_annotations(confidence_threshold=args.threshold)
    
    # 生成报告
    tester.generate_report(results, args.output)

if __name__ == "__main__":
    main()
