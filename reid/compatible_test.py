#!/usr/bin/env python3
"""
兼容性测试脚本 - 测试V1模型和V2模型
"""

import os
import sys
import random
import logging
import json
import torch
import numpy as np
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_v1_model(model_path: str, num_cats: int = 15):
    """测试V1模型 (使用原始的adaptive_recognizer)"""
    try:
        from adaptive_recognizer import AdaptiveCatRecognizer
        
        logger.info(f"🧪 测试V1模型: {model_path}")
        
        # 加载数据集分割信息
        with open('dataset_split.json', 'r') as f:
            split_info = json.load(f)
        
        test_cats = split_info['test_data']
        test_cat_ids = [cat['cat_id'] for cat in test_cats]
        
        # 构建测试集图片字典
        test_cat_images = {}
        for cat in test_cats:
            test_cat_images[cat['cat_id']] = cat['images']
        
        # 创建识别器
        recognizer = AdaptiveCatRecognizer(model_path, device='cpu')
        
        # 随机选择测试猫咪
        if len(test_cat_ids) < num_cats:
            selected_cats = test_cat_ids
        else:
            selected_cats = random.sample(test_cat_ids, num_cats)
        
        # 注册猫咪
        registered_cats = []
        for cat_id in selected_cats:
            if cat_id not in test_cat_images:
                continue
            
            images = test_cat_images[cat_id]
            if len(images) < 5:
                continue
            
            # 分割注册和测试图片
            train_count = max(3, int(len(images) * 0.7))
            train_images = images[:train_count]
            test_images = images[train_count:]
            
            # 注册猫咪
            result = recognizer.register_cat(cat_id, f"Cat_{cat_id}", train_images)
            if result['success'] and test_images:
                registered_cats.append((cat_id, test_images))
        
        if len(registered_cats) < 3:
            logger.warning(f"注册成功的猫咪数量不足: {len(registered_cats)}")
            return None
        
        # 识别测试
        correct = 0
        total = 0
        confidences = []
        similarities = []
        
        for cat_id, test_images in registered_cats:
            test_image = random.choice(test_images)
            result = recognizer.recognize_cat(test_image)
            
            total += 1
            is_correct = result.get('success') and result.get('cat_id') == cat_id
            
            if is_correct:
                correct += 1
                confidences.append(result.get('confidence', 0.0))
            
            similarities.append(result.get('similarity', 0.0))
            
            # 显示结果
            status = "✅" if is_correct else "❌"
            predicted = result.get('cat_id', 'unknown')
            similarity = result.get('similarity', 0.0)
            confidence = result.get('confidence', 0.0)
            
            logger.info(f"{status} 真实:{cat_id} 预测:{predicted} "
                      f"相似度:{similarity:.3f} 置信度:{confidence:.1%}")
        
        # 计算统计
        accuracy = correct / total if total > 0 else 0.0
        avg_confidence = np.mean(confidences) if confidences else 0.0
        avg_similarity = np.mean(similarities) if similarities else 0.0
        
        print(f"\n🎉 V1模型测试结果:")
        print(f"📊 准确率: {accuracy:.1%} ({correct}/{total})")
        print(f"📊 平均置信度: {avg_confidence:.1%}")
        print(f"📊 平均相似度: {avg_similarity:.3f}")
        print(f"📊 测试规模: {len(registered_cats)} 只猫咪")
        
        return {
            'model_type': 'V1',
            'model_path': model_path,
            'accuracy': accuracy,
            'avg_confidence': avg_confidence,
            'avg_similarity': avg_similarity,
            'total_cats': len(registered_cats)
        }
        
    except Exception as e:
        logger.error(f"V1模型测试失败: {e}")
        return None

def test_v2_model(model_path: str, num_cats: int = 15):
    """测试V2模型 (使用optimized_recognizer_v2)"""
    try:
        from optimized_recognizer_v2 import OptimizedCatRecognizer
        
        logger.info(f"🧪 测试V2模型: {model_path}")
        
        # 加载数据集分割信息
        with open('dataset_split.json', 'r') as f:
            split_info = json.load(f)
        
        test_cats = split_info['test_data']
        test_cat_ids = [cat['cat_id'] for cat in test_cats]
        
        # 构建测试集图片字典
        test_cat_images = {}
        for cat in test_cats:
            test_cat_images[cat['cat_id']] = cat['images']
        
        # 创建识别器
        recognizer = OptimizedCatRecognizer(model_path, device='cpu', strategy='adaptive')
        
        # 随机选择测试猫咪
        if len(test_cat_ids) < num_cats:
            selected_cats = test_cat_ids
        else:
            selected_cats = random.sample(test_cat_ids, num_cats)
        
        # 注册猫咪
        registered_cats = []
        for cat_id in selected_cats:
            if cat_id not in test_cat_images:
                continue
            
            images = test_cat_images[cat_id]
            if len(images) < 5:
                continue
            
            # 分割注册和测试图片
            train_count = max(3, int(len(images) * 0.7))
            train_images = images[:train_count]
            test_images = images[train_count:]
            
            # 注册猫咪
            result = recognizer.register_cat(cat_id, f"Cat_{cat_id}", train_images)
            if result['success'] and test_images:
                registered_cats.append((cat_id, test_images))
        
        if len(registered_cats) < 3:
            logger.warning(f"注册成功的猫咪数量不足: {len(registered_cats)}")
            return None
        
        # 识别测试
        correct = 0
        total = 0
        confidences = []
        similarities = []
        stabilities = []
        
        for cat_id, test_images in registered_cats:
            test_image = random.choice(test_images)
            result = recognizer.recognize_cat(test_image)
            
            total += 1
            is_correct = result.get('success') and result.get('cat_id') == cat_id
            
            if is_correct:
                correct += 1
                confidences.append(result.get('confidence', 0.0))
            
            similarities.append(result.get('similarity', 0.0))
            stabilities.append(result.get('stability', 0.0))
            
            # 显示结果
            status = "✅" if is_correct else "❌"
            predicted = result.get('cat_id', 'unknown')
            similarity = result.get('similarity', 0.0)
            confidence = result.get('confidence', 0.0)
            stability = result.get('stability', 0.0)
            
            logger.info(f"{status} 真实:{cat_id} 预测:{predicted} "
                      f"相似度:{similarity:.3f} 置信度:{confidence:.1%} 稳定性:{stability:.3f}")
        
        # 计算统计
        accuracy = correct / total if total > 0 else 0.0
        avg_confidence = np.mean(confidences) if confidences else 0.0
        avg_similarity = np.mean(similarities) if similarities else 0.0
        avg_stability = np.mean(stabilities) if stabilities else 0.0
        
        print(f"\n🎉 V2模型测试结果:")
        print(f"📊 准确率: {accuracy:.1%} ({correct}/{total})")
        print(f"📊 平均置信度: {avg_confidence:.1%}")
        print(f"📊 平均相似度: {avg_similarity:.3f}")
        print(f"📊 平均稳定性: {avg_stability:.3f}")
        print(f"📊 测试规模: {len(registered_cats)} 只猫咪")
        
        return {
            'model_type': 'V2',
            'model_path': model_path,
            'accuracy': accuracy,
            'avg_confidence': avg_confidence,
            'avg_similarity': avg_similarity,
            'avg_stability': avg_stability,
            'total_cats': len(registered_cats)
        }
        
    except Exception as e:
        logger.error(f"V2模型测试失败: {e}")
        return None

def main():
    """主函数"""
    print("🔍 兼容性测试 - V1 vs V2 模型对比")
    print("=" * 60)
    
    results = []
    
    # 测试V1模型
    v1_models = [
        ('fast_megadescriptor_100cats_best.pth', 'V1最佳模型'),
        ('fast_megadescriptor_100cats.pth', 'V1基础模型')
    ]
    
    for model_path, model_name in v1_models:
        if os.path.exists(model_path):
            print(f"\n{'='*60}")
            print(f"🧪 测试 {model_name}")
            print(f"📁 文件: {model_path}")
            print(f"{'='*60}")
            
            result = test_v1_model(model_path, num_cats=15)
            if result:
                result['model_name'] = model_name
                results.append(result)
        else:
            print(f"❌ 模型文件不存在: {model_path}")
    
    # 测试V2模型
    v2_models = [
        ('optimized_megadescriptor_v2.pth', 'V2优化模型')
    ]
    
    for model_path, model_name in v2_models:
        if os.path.exists(model_path):
            print(f"\n{'='*60}")
            print(f"🧪 测试 {model_name}")
            print(f"📁 文件: {model_path}")
            print(f"{'='*60}")
            
            result = test_v2_model(model_path, num_cats=15)
            if result:
                result['model_name'] = model_name
                results.append(result)
        else:
            print(f"⏳ V2模型还在训练中: {model_path}")
    
    # 显示总结
    print(f"\n{'='*80}")
    print(f"🏆 测试总结")
    print(f"{'='*80}")
    
    if results:
        # 按准确率排序
        results.sort(key=lambda x: x['accuracy'], reverse=True)
        
        print(f"{'排名':<4} {'模型':<20} {'版本':<6} {'准确率':<8} {'置信度':<8} {'相似度':<8}")
        print(f"-" * 70)
        
        for i, result in enumerate(results, 1):
            model_short = result['model_name'][:18] + '..' if len(result['model_name']) > 20 else result['model_name']
            
            print(f"{i:<4} {model_short:<20} {result['model_type']:<6} "
                  f"{result['accuracy']:.1%}    {result['avg_confidence']:.1%}    "
                  f"{result['avg_similarity']:.3f}")
        
        # 找到最佳模型
        best_result = results[0]
        print(f"\n🥇 最佳模型:")
        print(f"   模型: {best_result['model_name']}")
        print(f"   版本: {best_result['model_type']}")
        print(f"   准确率: {best_result['accuracy']:.1%}")
        print(f"   置信度: {best_result['avg_confidence']:.1%}")
        
        # 保存结果
        with open('compatible_test_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\n📁 详细结果已保存: compatible_test_results.json")
        
    else:
        print("❌ 没有成功的测试结果")

if __name__ == "__main__":
    main()
