#!/usr/bin/env python3
"""
全面验证续训效果
包括困难样本专门测试和多轮随机测试
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
from pathlib import Path
import random
import logging
import json
from typing import Dict, List, Tuple
from PIL import Image
import torchvision.transforms as transforms
from collections import defaultdict
import timm

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComprehensiveValidator:
    """全面验证器"""
    
    def __init__(self, split_file: str = 'dataset_split.json'):
        self.split_file = split_file
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # 加载数据集信息
        with open(split_file, 'r') as f:
            self.split_data = json.load(f)
        
        # 图像变换
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        logger.info("全面验证器初始化完成")
    
    def load_model(self, model_path: str):
        """加载模型"""
        checkpoint = torch.load(model_path, map_location=self.device, weights_only=False)
        
        # 根据模型类型创建模型
        model_type = checkpoint.get('model_type', 'FastMegaDescriptor')
        feature_dim = checkpoint.get('feature_dim', 1024)
        
        if model_type == 'FastMegaDescriptor':
            from fast_training_with_validation import FastMegaDescriptor
            model = FastMegaDescriptor(feature_dim=feature_dim)
        elif model_type == 'HardSampleContinuedModel':
            # 创建兼容的模型结构
            model = self._create_compatible_model(feature_dim)
        else:
            # 默认使用灵活模型
            model = self._create_flexible_model(feature_dim)
        
        model.load_state_dict(checkpoint['model_state_dict'])
        model = model.to(self.device)
        model.eval()
        
        logger.info(f"成功加载模型: {model_type}, 特征维度: {feature_dim}")
        return model
    
    def _create_compatible_model(self, feature_dim):
        """创建兼容的模型结构"""
        class CompatibleModel(nn.Module):
            def __init__(self, feature_dim):
                super().__init__()
                self.backbone = timm.create_model(
                    'hf-hub:BVRA/MegaDescriptor-T-224',
                    pretrained=True,
                    num_classes=0
                )
                
                with torch.no_grad():
                    dummy_input = torch.randn(1, 3, 224, 224)
                    backbone_output = self.backbone(dummy_input)
                    backbone_dim = backbone_output.shape[1]
                
                self.feature_enhancer = nn.Sequential(
                    nn.Linear(backbone_dim, feature_dim * 2),
                    nn.BatchNorm1d(feature_dim * 2),
                    nn.ReLU(),
                    nn.Dropout(0.2),
                    nn.Linear(feature_dim * 2, feature_dim),
                    nn.BatchNorm1d(feature_dim),
                    nn.ReLU(),
                    nn.Dropout(0.1),
                    nn.Linear(feature_dim, feature_dim)
                )
            
            def forward(self, x):
                backbone_features = self.backbone(x)
                enhanced_features = self.feature_enhancer(backbone_features)
                return torch.nn.functional.normalize(enhanced_features, p=2, dim=1)
        
        return CompatibleModel(feature_dim)
    
    def _create_flexible_model(self, feature_dim):
        """创建灵活的模型结构"""
        class FlexibleModel(nn.Module):
            def __init__(self, feature_dim):
                super().__init__()
                self.backbone = timm.create_model(
                    'hf-hub:BVRA/MegaDescriptor-T-224',
                    pretrained=True,
                    num_classes=0
                )
                
                with torch.no_grad():
                    dummy_input = torch.randn(1, 3, 224, 224)
                    backbone_output = self.backbone(dummy_input)
                    backbone_dim = backbone_output.shape[1]
                
                self.feature_enhancer = nn.Sequential(
                    nn.Linear(backbone_dim, feature_dim * 2),
                    nn.BatchNorm1d(feature_dim * 2),
                    nn.ReLU(),
                    nn.Dropout(0.2),
                    nn.Linear(feature_dim * 2, feature_dim),
                    nn.BatchNorm1d(feature_dim),
                    nn.ReLU(),
                    nn.Dropout(0.1),
                    nn.Linear(feature_dim, feature_dim)
                )
            
            def forward(self, x):
                backbone_features = self.backbone(x)
                enhanced_features = self.feature_enhancer(backbone_features)
                return torch.nn.functional.normalize(enhanced_features, p=2, dim=1)
        
        return FlexibleModel(feature_dim)
    
    def extract_features(self, model, image_path: str) -> np.ndarray:
        """提取图像特征"""
        try:
            image = Image.open(image_path).convert('RGB')
            image_tensor = self.transform(image).unsqueeze(0).to(self.device)
            
            with torch.no_grad():
                features = model(image_tensor)
                return features.cpu().numpy().flatten()
        except Exception as e:
            logger.error(f"特征提取失败 {image_path}: {e}")
            return None
    
    def test_hard_samples(self, model, corner_case_file: str = 'corner_case_analysis.json') -> Dict:
        """专门测试困难样本"""
        logger.info("开始困难样本专门测试")
        
        # 加载困难样本分析
        if not os.path.exists(corner_case_file):
            logger.warning(f"困难样本分析文件不存在: {corner_case_file}")
            return {}
        
        with open(corner_case_file, 'r') as f:
            corner_analysis = json.load(f)
        
        # 提取困难样本猫咪ID
        hard_sample_cats = set()
        for case in corner_analysis.get('corner_cases', []):
            hard_sample_cats.add(case['cat_id'])
        
        for cat_id in corner_analysis.get('confidence_issues', []):
            if isinstance(cat_id, dict) and 'cat_id' in cat_id:
                hard_sample_cats.add(cat_id['cat_id'])
        
        hard_sample_cats = list(hard_sample_cats)[:15]  # 最多测试15只困难样本猫咪
        
        if not hard_sample_cats:
            logger.warning("没有找到困难样本猫咪")
            return {}
        
        logger.info(f"测试 {len(hard_sample_cats)} 只困难样本猫咪")
        
        # 构建特征数据库
        cat_features = {}
        for cat_id in hard_sample_cats:
            cat_dir = f"../dataset/cat_individual_images/{cat_id}"
            if not os.path.exists(cat_dir):
                continue
            
            image_files = [f for f in os.listdir(cat_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
            if len(image_files) < 6:  # 需要足够的图片
                continue
            
            # 使用前3张图片注册
            register_images = image_files[:3]
            features_list = []
            
            for img_file in register_images:
                full_path = os.path.join(cat_dir, img_file)
                features = self.extract_features(model, full_path)
                if features is not None:
                    features_list.append(features)
            
            if features_list:
                cat_features[cat_id] = np.array(features_list)
        
        # 识别测试
        correct_predictions = 0
        total_predictions = 0
        confidences = []
        similarities = []
        
        for cat_id in cat_features.keys():
            cat_dir = f"../dataset/cat_individual_images/{cat_id}"
            image_files = [f for f in os.listdir(cat_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
            
            # 使用后面的图片进行测试
            test_images = image_files[3:6]  # 测试3张
            
            for img_file in test_images:
                full_path = os.path.join(cat_dir, img_file)
                query_features = self.extract_features(model, full_path)
                
                if query_features is None:
                    continue
                
                # 计算与所有注册猫咪的相似度
                best_match_id = None
                best_similarity = -1.0
                
                for registered_cat_id, registered_features in cat_features.items():
                    # 计算与该猫咪所有注册图片的相似度
                    similarities_with_cat = []
                    for reg_feature in registered_features:
                        sim = np.dot(query_features, reg_feature)
                        similarities_with_cat.append(sim)
                    
                    # 使用最大相似度
                    max_sim = np.max(similarities_with_cat)
                    
                    if max_sim > best_similarity:
                        best_similarity = max_sim
                        best_match_id = registered_cat_id
                
                total_predictions += 1
                similarities.append(best_similarity)
                
                # 自适应阈值
                threshold = 0.75  # 困难样本使用固定阈值
                
                if best_similarity >= threshold and best_match_id == cat_id:
                    correct_predictions += 1
                    confidences.append(best_similarity)
        
        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0.0
        avg_confidence = np.mean(confidences) if confidences else 0.0
        avg_similarity = np.mean(similarities) if similarities else 0.0
        
        result = {
            'accuracy': accuracy,
            'correct_predictions': correct_predictions,
            'total_predictions': total_predictions,
            'avg_confidence': avg_confidence,
            'avg_similarity': avg_similarity,
            'num_hard_cats': len(cat_features),
            'hard_sample_cats': list(cat_features.keys())
        }
        
        logger.info(f"困难样本测试完成: 准确率={accuracy:.1%}, 平均相似度={avg_similarity:.3f}")
        
        return result
    
    def multi_round_test(self, model, rounds: int = 5, cats_per_round: int = 15) -> Dict:
        """多轮随机测试"""
        logger.info(f"开始多轮随机测试: {rounds} 轮, 每轮 {cats_per_round} 只猫咪")
        
        test_cat_ids = self.split_data.get('test_cat_ids', [])
        if not test_cat_ids:
            logger.error("数据集分割文件中没有找到test_cat_ids")
            return {}
        
        round_results = []
        
        for round_idx in range(rounds):
            logger.info(f"第 {round_idx + 1} 轮测试...")
            
            # 随机选择猫咪
            test_cats = random.sample(test_cat_ids, min(cats_per_round, len(test_cat_ids)))
            
            # 构建特征数据库
            cat_features = {}
            for cat_id in test_cats:
                cat_dir = f"../dataset/cat_individual_images/{cat_id}"
                if not os.path.exists(cat_dir):
                    continue
                
                image_files = [f for f in os.listdir(cat_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                if len(image_files) < 6:
                    continue
                
                # 随机选择注册图片
                random.shuffle(image_files)
                register_images = image_files[:3]
                features_list = []
                
                for img_file in register_images:
                    full_path = os.path.join(cat_dir, img_file)
                    features = self.extract_features(model, full_path)
                    if features is not None:
                        features_list.append(features)
                
                if features_list:
                    cat_features[cat_id] = np.array(features_list)
            
            # 识别测试
            correct_predictions = 0
            total_predictions = 0
            
            for cat_id in cat_features.keys():
                cat_dir = f"../dataset/cat_individual_images/{cat_id}"
                image_files = [f for f in os.listdir(cat_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                
                # 使用未注册的图片进行测试
                test_images = [img for img in image_files if img not in image_files[:3]][:2]
                
                for img_file in test_images:
                    full_path = os.path.join(cat_dir, img_file)
                    query_features = self.extract_features(model, full_path)
                    
                    if query_features is None:
                        continue
                    
                    # 计算与所有注册猫咪的相似度
                    best_match_id = None
                    best_similarity = -1.0
                    
                    for registered_cat_id, registered_features in cat_features.items():
                        similarities_with_cat = []
                        for reg_feature in registered_features:
                            sim = np.dot(query_features, reg_feature)
                            similarities_with_cat.append(sim)
                        
                        max_sim = np.max(similarities_with_cat)
                        
                        if max_sim > best_similarity:
                            best_similarity = max_sim
                            best_match_id = registered_cat_id
                    
                    total_predictions += 1
                    
                    # 自适应阈值
                    threshold = 0.75
                    
                    if best_similarity >= threshold and best_match_id == cat_id:
                        correct_predictions += 1
            
            round_accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0.0
            round_results.append({
                'round': round_idx + 1,
                'accuracy': round_accuracy,
                'correct': correct_predictions,
                'total': total_predictions,
                'num_cats': len(cat_features)
            })
            
            logger.info(f"第 {round_idx + 1} 轮结果: 准确率={round_accuracy:.1%}")
        
        # 计算总体统计
        accuracies = [r['accuracy'] for r in round_results]
        avg_accuracy = np.mean(accuracies)
        std_accuracy = np.std(accuracies)
        
        result = {
            'rounds': round_results,
            'avg_accuracy': avg_accuracy,
            'std_accuracy': std_accuracy,
            'min_accuracy': np.min(accuracies),
            'max_accuracy': np.max(accuracies)
        }
        
        logger.info(f"多轮测试完成: 平均准确率={avg_accuracy:.1%} ± {std_accuracy:.1%}")
        
        return result
    
    def comprehensive_comparison(self, base_model_path: str, continued_model_path: str) -> Dict:
        """全面对比两个模型"""
        logger.info("开始全面模型对比")
        
        results = {
            'base_model': {'path': base_model_path},
            'continued_model': {'path': continued_model_path}
        }
        
        # 测试基础模型
        logger.info("测试基础模型...")
        base_model = self.load_model(base_model_path)
        
        results['base_model']['hard_samples'] = self.test_hard_samples(base_model)
        results['base_model']['multi_round'] = self.multi_round_test(base_model, rounds=3, cats_per_round=15)
        
        # 测试续训模型
        logger.info("测试续训模型...")
        continued_model = self.load_model(continued_model_path)
        
        results['continued_model']['hard_samples'] = self.test_hard_samples(continued_model)
        results['continued_model']['multi_round'] = self.multi_round_test(continued_model, rounds=3, cats_per_round=15)
        
        # 计算改善
        base_hard_acc = results['base_model']['hard_samples'].get('accuracy', 0)
        cont_hard_acc = results['continued_model']['hard_samples'].get('accuracy', 0)
        base_multi_acc = results['base_model']['multi_round'].get('avg_accuracy', 0)
        cont_multi_acc = results['continued_model']['multi_round'].get('avg_accuracy', 0)
        
        results['comparison'] = {
            'hard_samples_improvement': cont_hard_acc - base_hard_acc,
            'multi_round_improvement': cont_multi_acc - base_multi_acc,
            'overall_assessment': self._assess_improvement(cont_hard_acc - base_hard_acc, cont_multi_acc - base_multi_acc)
        }
        
        return results
    
    def _assess_improvement(self, hard_improvement: float, multi_improvement: float) -> str:
        """评估改善程度"""
        if hard_improvement > 0.1 or multi_improvement > 0.05:
            return "显著改善"
        elif hard_improvement > 0.05 or multi_improvement > 0.02:
            return "适度改善"
        elif hard_improvement > -0.05 and multi_improvement > -0.02:
            return "性能相当"
        else:
            return "需要进一步优化"

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='全面验证续训效果')
    parser.add_argument('--base-model', type=str, default='fast_megadescriptor_100cats_best.pth', 
                       help='基础模型路径')
    parser.add_argument('--continued-model', type=str, default='hard_sample_continued_model.pth', 
                       help='续训模型路径')
    parser.add_argument('--output', type=str, default='comprehensive_validation_results.json', help='输出文件')
    
    args = parser.parse_args()
    
    # 检查模型文件
    if not os.path.exists(args.base_model):
        print(f"❌ 基础模型文件不存在: {args.base_model}")
        return
    
    if not os.path.exists(args.continued_model):
        print(f"❌ 续训模型文件不存在: {args.continued_model}")
        return
    
    # 创建验证器
    validator = ComprehensiveValidator()
    
    # 全面对比
    results = validator.comprehensive_comparison(args.base_model, args.continued_model)
    
    # 保存结果
    with open(args.output, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    # 显示总结
    print("\n" + "="*60)
    print("🔍 全面验证结果")
    print("="*60)
    
    base_hard = results['base_model']['hard_samples'].get('accuracy', 0)
    cont_hard = results['continued_model']['hard_samples'].get('accuracy', 0)
    base_multi = results['base_model']['multi_round'].get('avg_accuracy', 0)
    cont_multi = results['continued_model']['multi_round'].get('avg_accuracy', 0)
    
    print(f"\n📊 困难样本测试:")
    print(f"   基础模型: {base_hard:.1%}")
    print(f"   续训模型: {cont_hard:.1%}")
    print(f"   改善幅度: {cont_hard - base_hard:+.1%}")
    
    print(f"\n📊 多轮随机测试:")
    print(f"   基础模型: {base_multi:.1%}")
    print(f"   续训模型: {cont_multi:.1%}")
    print(f"   改善幅度: {cont_multi - base_multi:+.1%}")
    
    assessment = results['comparison']['overall_assessment']
    print(f"\n🎯 总体评价: {assessment}")
    
    print(f"\n📁 详细结果已保存: {args.output}")

if __name__ == "__main__":
    main()
