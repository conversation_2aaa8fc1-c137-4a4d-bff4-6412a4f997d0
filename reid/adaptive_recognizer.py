#!/usr/bin/env python3
"""
自适应识别器 - 基于测试结果优化的最终版本
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from pathlib import Path
import time
from collections import defaultdict
from PIL import Image
import torchvision.transforms as transforms
import timm

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, 'training'))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedMegaDescriptor(nn.Module):
    """增强版MegaDescriptor"""
    
    def __init__(self, feature_dim=512):
        super().__init__()
        
        self.backbone = timm.create_model(
            'hf-hub:BVRA/MegaDescriptor-T-224',
            pretrained=True,
            num_classes=0
        )
        
        with torch.no_grad():
            dummy_input = torch.randn(1, 3, 224, 224)
            backbone_output = self.backbone(dummy_input)
            backbone_dim = backbone_output.shape[1]
        
        self.feature_enhancer = nn.Sequential(
            nn.Linear(backbone_dim, feature_dim * 2),
            nn.BatchNorm1d(feature_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(feature_dim * 2, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(feature_dim, feature_dim)
        )
    
    def forward(self, x):
        backbone_features = self.backbone(x)
        enhanced_features = self.feature_enhancer(backbone_features)
        return torch.nn.functional.normalize(enhanced_features, p=2, dim=1)

class AdaptiveSimilarityMatcher:
    """自适应相似度匹配器 - 基于实际测试结果优化"""
    
    def __init__(self):
        self.feature_database = {}
        
        # 基于实际测试结果调整的阈值策略
        self.threshold_strategies = {
            'conservative': {  # 保守策略 - 更宽松的阈值
                3: 0.75,
                5: 0.75,   # 从0.85降低到0.75
                10: 0.80,  # 从0.83降低到0.80
                20: 0.85,  # 从0.88降低到0.85
                30: 0.80,
                50: 0.75
            },
            'balanced': {  # 平衡策略 - 基于测试结果的最佳平衡
                3: 0.70,
                5: 0.70,   # 更宽松
                10: 0.75,  # 适中
                20: 0.80,  # 稍严格
                30: 0.75,
                50: 0.70
            },
            'aggressive': {  # 激进策略 - 更严格的阈值
                3: 0.80,
                5: 0.80,
                10: 0.83,
                20: 0.88,
                30: 0.85,
                50: 0.80
            }
        }
        
        # 默认使用平衡策略
        self.current_strategy = 'balanced'
        
    def add_cat_features(self, cat_id: str, features: np.ndarray):
        """添加猫咪特征"""
        if cat_id not in self.feature_database:
            self.feature_database[cat_id] = []
        
        self.feature_database[cat_id].append(features)
        
        if len(self.feature_database[cat_id]) > 15:
            self.feature_database[cat_id] = self.feature_database[cat_id][-15:]
    
    def compute_similarity(self, query_features: np.ndarray, cat_id: str) -> float:
        """计算相似度"""
        if cat_id not in self.feature_database:
            return 0.0
        
        cat_features_list = self.feature_database[cat_id]
        
        similarities = []
        for cat_features in cat_features_list:
            sim = np.dot(query_features, cat_features)
            similarities.append(sim)
        
        if not similarities:
            return 0.0
        
        similarities = np.array(similarities)
        
        # 使用加权平均：最高相似度权重更大
        sorted_sims = np.sort(similarities)[::-1]
        
        if len(sorted_sims) >= 3:
            weights = np.array([0.5, 0.3, 0.2])
            weighted_sim = np.average(sorted_sims[:3], weights=weights)
        elif len(sorted_sims) == 2:
            weights = np.array([0.7, 0.3])
            weighted_sim = np.average(sorted_sims[:2], weights=weights)
        else:
            weighted_sim = sorted_sims[0]
        
        return float(weighted_sim)
    
    def find_best_matches(self, query_features: np.ndarray, top_k: int = 5) -> List[Tuple[str, float]]:
        """找到最佳匹配"""
        if not self.feature_database:
            return []
        
        matches = []
        for cat_id in self.feature_database:
            similarity = self.compute_similarity(query_features, cat_id)
            matches.append((cat_id, similarity))
        
        matches.sort(key=lambda x: x[1], reverse=True)
        return matches[:top_k]
    
    def get_adaptive_threshold(self, num_cats: int, strategy: str = None) -> float:
        """获取自适应阈值"""
        if strategy is None:
            strategy = self.current_strategy
        
        thresholds = self.threshold_strategies[strategy]
        
        if num_cats in thresholds:
            return thresholds[num_cats]
        
        # 线性插值
        sorted_scales = sorted(thresholds.keys())
        
        if num_cats <= sorted_scales[0]:
            return thresholds[sorted_scales[0]]
        elif num_cats >= sorted_scales[-1]:
            return thresholds[sorted_scales[-1]]
        else:
            for i in range(len(sorted_scales) - 1):
                if sorted_scales[i] <= num_cats <= sorted_scales[i + 1]:
                    x1, y1 = sorted_scales[i], thresholds[sorted_scales[i]]
                    x2, y2 = sorted_scales[i + 1], thresholds[sorted_scales[i + 1]]
                    return y1 + (y2 - y1) * (num_cats - x1) / (x2 - x1)
        
        return 0.75  # 默认值
    
    def set_strategy(self, strategy: str):
        """设置阈值策略"""
        if strategy in self.threshold_strategies:
            self.current_strategy = strategy
            logger.info(f"阈值策略已切换到: {strategy}")
        else:
            logger.warning(f"未知策略: {strategy}")

class AdaptiveCatRecognizer:
    """自适应猫咪识别器"""
    
    def __init__(self, model_path: str = None, device='cuda', strategy='balanced'):
        self.device = device if torch.cuda.is_available() else 'cpu'
        
        # 加载模型
        self.model = self._load_model(model_path)
        
        # 自适应相似度匹配器
        self.similarity_matcher = AdaptiveSimilarityMatcher()
        self.similarity_matcher.set_strategy(strategy)
        
        # 图像预处理
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        # 猫咪数据库
        self.cat_database = {}
        
        # 性能统计
        self.stats = {
            'total_registrations': 0,
            'total_recognitions': 0,
            'correct_recognitions': 0,
            'avg_confidence': 0.0,
            'strategy_performance': defaultdict(list)
        }
        
        logger.info(f"自适应猫咪识别系统初始化完成 (策略: {strategy})")
    
    def _load_model(self, model_path: str = None) -> nn.Module:
        """加载模型"""
        try:
            model = EnhancedMegaDescriptor(feature_dim=512).to(self.device)
            
            if model_path and os.path.exists(model_path):
                checkpoint = torch.load(model_path, map_location=self.device)
                model.load_state_dict(checkpoint['model_state_dict'])
                logger.info(f"成功加载增强模型: {model_path}")
            else:
                logger.info("使用预训练的MegaDescriptor模型")
            
            model.eval()
            return model
            
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            raise
    
    def extract_features(self, image_tensor: torch.Tensor) -> np.ndarray:
        """提取特征"""
        try:
            with torch.no_grad():
                if image_tensor.dim() == 3:
                    image_tensor = image_tensor.unsqueeze(0)
                
                image_tensor = image_tensor.to(self.device)
                features = self.model(image_tensor)
                
                if features.dim() > 2:
                    features = features.view(features.size(0), -1)
                
                return features.cpu().numpy().flatten()
        except Exception as e:
            logger.error(f"特征提取失败: {e}")
            return np.random.randn(512).astype(np.float32)
    
    def register_cat(self, cat_id: str, cat_name: str, image_paths: List[str]) -> Dict:
        """注册猫咪"""
        try:
            if len(image_paths) < 3:
                return {
                    'success': False,
                    'error': f'图片数量不足，需要至少3张，提供了{len(image_paths)}张'
                }
            
            valid_images = 0
            
            for img_path in image_paths:
                try:
                    image = Image.open(img_path).convert('RGB')
                    image_tensor = self.transform(image)
                    features = self.extract_features(image_tensor)
                    self.similarity_matcher.add_cat_features(cat_id, features)
                    valid_images += 1
                        
                except Exception as e:
                    logger.warning(f"处理图片失败 {img_path}: {e}")
                    continue
            
            if valid_images < 3:
                return {
                    'success': False,
                    'error': f'有效图片不足，需要至少3张，实际{valid_images}张'
                }
            
            self.cat_database[cat_id] = {
                'name': cat_name,
                'image_count': valid_images,
                'registration_time': time.time()
            }
            
            self.stats['total_registrations'] += 1
            
            logger.info(f"成功注册猫咪: {cat_name} (ID: {cat_id}), 使用{valid_images}张图片")
            
            return {
                'success': True,
                'cat_id': cat_id,
                'cat_name': cat_name,
                'images_used': valid_images
            }
            
        except Exception as e:
            logger.error(f"注册猫咪失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def recognize_cat(self, image_path: str, strategy: str = None) -> Dict:
        """识别猫咪"""
        try:
            image = Image.open(image_path).convert('RGB')
            image_tensor = self.transform(image)
            query_features = self.extract_features(image_tensor)
            
            matches = self.similarity_matcher.find_best_matches(query_features, top_k=5)
            
            if not matches:
                return {
                    'success': False,
                    'message': '没有注册的猫咪',
                    'status': 'no_cats'
                }
            
            best_match_id, best_similarity = matches[0]
            
            # 使用指定策略或当前策略的阈值
            num_cats = len(self.cat_database)
            adaptive_threshold = self.similarity_matcher.get_adaptive_threshold(num_cats, strategy)
            
            self.stats['total_recognitions'] += 1
            
            if best_similarity >= adaptive_threshold:
                cat_info = self.cat_database[best_match_id]
                confidence = min(0.99, best_similarity * 1.05)
                
                self.stats['correct_recognitions'] += 1
                self.stats['avg_confidence'] = (
                    self.stats['avg_confidence'] * 0.9 + confidence * 0.1
                )
                
                # 记录策略性能
                used_strategy = strategy or self.similarity_matcher.current_strategy
                self.stats['strategy_performance'][used_strategy].append(1.0)
                
                return {
                    'success': True,
                    'cat_id': best_match_id,
                    'cat_name': cat_info['name'],
                    'confidence': confidence,
                    'similarity': best_similarity,
                    'threshold_used': adaptive_threshold,
                    'strategy_used': used_strategy,
                    'status': 'recognized',
                    'top_matches': [
                        {
                            'cat_id': cat_id,
                            'cat_name': self.cat_database[cat_id]['name'],
                            'similarity': similarity
                        }
                        for cat_id, similarity in matches[:3]
                    ]
                }
            else:
                # 记录策略性能
                used_strategy = strategy or self.similarity_matcher.current_strategy
                self.stats['strategy_performance'][used_strategy].append(0.0)
                
                return {
                    'success': False,
                    'message': f'相似度过低 ({best_similarity:.3f} < {adaptive_threshold:.3f})',
                    'status': 'unknown',
                    'threshold_used': adaptive_threshold,
                    'strategy_used': used_strategy,
                    'best_match': {
                        'cat_id': best_match_id,
                        'cat_name': self.cat_database[best_match_id]['name'],
                        'similarity': best_similarity
                    }
                }
                
        except Exception as e:
            logger.error(f"识别猫咪失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def test_strategies(self, test_data: List[Tuple[str, str]]) -> Dict:
        """测试不同策略的性能"""
        strategies = ['conservative', 'balanced', 'aggressive']
        strategy_results = {}
        
        for strategy in strategies:
            correct = 0
            total = 0
            
            for cat_id, image_path in test_data:
                result = self.recognize_cat(image_path, strategy=strategy)
                total += 1
                if result.get('success') and result.get('cat_id') == cat_id:
                    correct += 1
            
            accuracy = correct / total if total > 0 else 0.0
            strategy_results[strategy] = {
                'accuracy': accuracy,
                'correct': correct,
                'total': total
            }
        
        return strategy_results
    
    def get_system_stats(self) -> Dict:
        """获取系统统计信息"""
        accuracy = (self.stats['correct_recognitions'] / 
                   max(self.stats['total_recognitions'], 1))
        
        # 计算各策略性能
        strategy_performance = {}
        for strategy, results in self.stats['strategy_performance'].items():
            if results:
                strategy_performance[strategy] = {
                    'accuracy': np.mean(results),
                    'count': len(results)
                }
        
        return {
            'registered_cats': len(self.cat_database),
            'total_registrations': self.stats['total_registrations'],
            'total_recognitions': self.stats['total_recognitions'],
            'accuracy': accuracy,
            'avg_confidence': self.stats['avg_confidence'],
            'current_strategy': self.similarity_matcher.current_strategy,
            'current_threshold': self.similarity_matcher.get_adaptive_threshold(len(self.cat_database)),
            'strategy_performance': strategy_performance,
            'available_strategies': list(self.similarity_matcher.threshold_strategies.keys())
        }

def create_adaptive_recognizer(model_path: str = None, device='cuda', strategy='balanced'):
    """创建自适应识别器的工厂函数"""
    return AdaptiveCatRecognizer(model_path, device, strategy)

def main():
    """测试自适应识别器"""
    import argparse
    import random
    
    parser = argparse.ArgumentParser(description='自适应识别器测试')
    parser.add_argument('--dataset', type=str, 
                       default='/home/<USER>/animsi/caby_training/dataset/cat_individual_images',
                       help='数据集路径')
    parser.add_argument('--model', type=str, 
                       default='training/enhanced_megadescriptor_50cats.pth',
                       help='模型路径')
    parser.add_argument('--cats', type=int, default=5,
                       help='测试猫咪数量')
    parser.add_argument('--strategy', type=str, default='balanced',
                       choices=['conservative', 'balanced', 'aggressive'],
                       help='阈值策略')
    
    args = parser.parse_args()
    
    # 加载数据
    dataset_path = Path(args.dataset)
    available_cats = []
    
    for cat_folder in dataset_path.iterdir():
        if not cat_folder.is_dir() or not cat_folder.name.isdigit():
            continue
        
        images = []
        for ext in ['*.jpg', '*.JPG', '*.jpeg', '*.JPEG', '*.png', '*.PNG']:
            images.extend(list(cat_folder.glob(ext)))
        
        if len(images) >= 5:
            available_cats.append((cat_folder.name, [str(img) for img in images]))
    
    available_cats.sort(key=lambda x: len(x[1]), reverse=True)
    
    print(f"🚀 自适应识别器测试 (策略: {args.strategy})")
    print("=" * 60)
    print(f"使用 {args.cats} 只猫咪进行测试")
    
    # 创建自适应识别器
    recognizer = create_adaptive_recognizer(args.model, device='cpu', strategy=args.strategy)
    
    # 随机选择猫咪
    selected_cats = random.sample(available_cats, args.cats)
    
    # 注册和测试
    registered_cats = []
    test_data = []
    
    for cat_id, image_paths in selected_cats:
        train_count = max(3, int(len(image_paths) * 0.7))
        train_images = image_paths[:train_count]
        test_images = image_paths[train_count:]
        
        result = recognizer.register_cat(cat_id, f"Cat_{cat_id}", train_images)
        if result['success'] and test_images:
            registered_cats.append((cat_id, test_images))
            test_data.append((cat_id, random.choice(test_images)))
    
    # 测试所有策略
    print(f"\n📊 策略性能对比:")
    strategy_results = recognizer.test_strategies(test_data)
    
    for strategy, result in strategy_results.items():
        print(f"   {strategy:>12}: {result['accuracy']:.1%} ({result['correct']}/{result['total']})")
    
    # 显示最佳策略
    best_strategy = max(strategy_results.keys(), key=lambda s: strategy_results[s]['accuracy'])
    best_accuracy = strategy_results[best_strategy]['accuracy']
    
    print(f"\n🎯 最佳策略: {best_strategy} (准确率: {best_accuracy:.1%})")
    
    # 系统统计
    stats = recognizer.get_system_stats()
    print(f"\n🔧 系统统计:")
    print(f"   注册猫咪数: {stats['registered_cats']}")
    print(f"   当前策略: {stats['current_strategy']}")
    print(f"   当前阈值: {stats['current_threshold']:.3f}")

if __name__ == "__main__":
    main()
