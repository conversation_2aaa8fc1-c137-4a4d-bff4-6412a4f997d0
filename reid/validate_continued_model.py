#!/usr/bin/env python3
"""
验证续训模型效果
对比续训前后的模型性能
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
from pathlib import Path
import random
import logging
import json
from typing import Dict, List, Tuple
from PIL import Image
import torchvision.transforms as transforms
from collections import defaultdict
import timm

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ModelValidator:
    """模型验证器"""
    
    def __init__(self, split_file: str = 'dataset_split.json'):
        self.split_file = split_file
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # 加载数据集信息
        with open(split_file, 'r') as f:
            self.split_data = json.load(f)
        
        # 图像变换
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        logger.info("模型验证器初始化完成")
    
    def load_model(self, model_path: str):
        """加载模型"""
        checkpoint = torch.load(model_path, map_location=self.device, weights_only=False)
        
        # 根据模型类型创建模型
        model_type = checkpoint.get('model_type', 'FastMegaDescriptor')
        feature_dim = checkpoint.get('feature_dim', 1024)
        
        if model_type == 'FastMegaDescriptor':
            from fast_training_with_validation import FastMegaDescriptor
            model = FastMegaDescriptor(feature_dim=feature_dim)
        elif model_type == 'HardSampleContinuedModel':
            # 创建兼容的模型结构
            model = self._create_compatible_model(feature_dim)
        else:
            # 默认使用灵活模型
            model = self._create_flexible_model(feature_dim)
        
        model.load_state_dict(checkpoint['model_state_dict'])
        model = model.to(self.device)
        model.eval()
        
        logger.info(f"成功加载模型: {model_type}, 特征维度: {feature_dim}")
        return model
    
    def _create_compatible_model(self, feature_dim):
        """创建兼容的模型结构"""
        class CompatibleModel(nn.Module):
            def __init__(self, feature_dim):
                super().__init__()
                self.backbone = timm.create_model(
                    'hf-hub:BVRA/MegaDescriptor-T-224',
                    pretrained=True,
                    num_classes=0
                )
                
                with torch.no_grad():
                    dummy_input = torch.randn(1, 3, 224, 224)
                    backbone_output = self.backbone(dummy_input)
                    backbone_dim = backbone_output.shape[1]
                
                self.feature_enhancer = nn.Sequential(
                    nn.Linear(backbone_dim, feature_dim * 2),
                    nn.BatchNorm1d(feature_dim * 2),
                    nn.ReLU(),
                    nn.Dropout(0.2),
                    nn.Linear(feature_dim * 2, feature_dim),
                    nn.BatchNorm1d(feature_dim),
                    nn.ReLU(),
                    nn.Dropout(0.1),
                    nn.Linear(feature_dim, feature_dim)
                )
            
            def forward(self, x):
                backbone_features = self.backbone(x)
                enhanced_features = self.feature_enhancer(backbone_features)
                return torch.nn.functional.normalize(enhanced_features, p=2, dim=1)
        
        return CompatibleModel(feature_dim)
    
    def _create_flexible_model(self, feature_dim):
        """创建灵活的模型结构"""
        class FlexibleModel(nn.Module):
            def __init__(self, feature_dim):
                super().__init__()
                self.backbone = timm.create_model(
                    'hf-hub:BVRA/MegaDescriptor-T-224',
                    pretrained=True,
                    num_classes=0
                )
                
                with torch.no_grad():
                    dummy_input = torch.randn(1, 3, 224, 224)
                    backbone_output = self.backbone(dummy_input)
                    backbone_dim = backbone_output.shape[1]
                
                self.feature_enhancer = nn.Sequential(
                    nn.Linear(backbone_dim, feature_dim * 2),
                    nn.BatchNorm1d(feature_dim * 2),
                    nn.ReLU(),
                    nn.Dropout(0.2),
                    nn.Linear(feature_dim * 2, feature_dim),
                    nn.BatchNorm1d(feature_dim),
                    nn.ReLU(),
                    nn.Dropout(0.1),
                    nn.Linear(feature_dim, feature_dim)
                )
            
            def forward(self, x):
                backbone_features = self.backbone(x)
                enhanced_features = self.feature_enhancer(backbone_features)
                return torch.nn.functional.normalize(enhanced_features, p=2, dim=1)
        
        return FlexibleModel(feature_dim)
    
    def extract_features(self, model, image_path: str) -> np.ndarray:
        """提取图像特征"""
        try:
            image = Image.open(image_path).convert('RGB')
            image_tensor = self.transform(image).unsqueeze(0).to(self.device)
            
            with torch.no_grad():
                features = model(image_tensor)
                return features.cpu().numpy().flatten()
        except Exception as e:
            logger.error(f"特征提取失败 {image_path}: {e}")
            return None
    
    def test_recognition_accuracy(self, model, num_cats: int = 20, samples_per_cat: int = 5) -> Dict:
        """测试识别准确率"""
        logger.info(f"开始识别准确率测试: {num_cats} 只猫咪, 每只 {samples_per_cat} 张图片")
        
        # 选择测试猫咪
        test_cat_ids = self.split_data.get('test_cat_ids', [])
        if not test_cat_ids:
            logger.error("数据集分割文件中没有找到test_cat_ids")
            return {}
        
        test_cats = random.sample(test_cat_ids, min(num_cats, len(test_cat_ids)))
        
        # 构建特征数据库（注册阶段）
        cat_features = {}
        for cat_id in test_cats:
            cat_dir = f"../dataset/cat_individual_images/{cat_id}"
            if not os.path.exists(cat_dir):
                continue
            
            image_files = [f for f in os.listdir(cat_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
            if len(image_files) < samples_per_cat + 1:  # 需要额外的图片用于测试
                continue
            
            # 使用前samples_per_cat张图片注册
            register_images = image_files[:samples_per_cat]
            features_list = []
            
            for img_file in register_images:
                full_path = os.path.join(cat_dir, img_file)
                features = self.extract_features(model, full_path)
                if features is not None:
                    features_list.append(features)
            
            if features_list:
                cat_features[cat_id] = np.array(features_list)
        
        logger.info(f"成功注册 {len(cat_features)} 只猫咪")
        
        # 识别测试
        correct_predictions = 0
        total_predictions = 0
        confidences = []
        similarities = []
        
        for cat_id in cat_features.keys():
            cat_dir = f"../dataset/cat_individual_images/{cat_id}"
            image_files = [f for f in os.listdir(cat_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
            
            # 使用后面的图片进行测试
            test_images = image_files[samples_per_cat:samples_per_cat+3]  # 最多测试3张
            
            for img_file in test_images:
                full_path = os.path.join(cat_dir, img_file)
                query_features = self.extract_features(model, full_path)
                
                if query_features is None:
                    continue
                
                # 计算与所有注册猫咪的相似度
                best_match_id = None
                best_similarity = -1.0
                
                for registered_cat_id, registered_features in cat_features.items():
                    # 计算与该猫咪所有注册图片的相似度
                    similarities_with_cat = []
                    for reg_feature in registered_features:
                        sim = np.dot(query_features, reg_feature)
                        similarities_with_cat.append(sim)
                    
                    # 使用最大相似度
                    max_sim = np.max(similarities_with_cat)
                    
                    if max_sim > best_similarity:
                        best_similarity = max_sim
                        best_match_id = registered_cat_id
                
                total_predictions += 1
                similarities.append(best_similarity)
                
                # 自适应阈值
                threshold = self._get_adaptive_threshold(len(cat_features))
                
                if best_similarity >= threshold and best_match_id == cat_id:
                    correct_predictions += 1
                    confidences.append(best_similarity)
        
        accuracy = correct_predictions / total_predictions if total_predictions > 0 else 0.0
        avg_confidence = np.mean(confidences) if confidences else 0.0
        avg_similarity = np.mean(similarities) if similarities else 0.0
        
        result = {
            'accuracy': accuracy,
            'correct_predictions': correct_predictions,
            'total_predictions': total_predictions,
            'avg_confidence': avg_confidence,
            'avg_similarity': avg_similarity,
            'num_cats': len(cat_features)
        }
        
        logger.info(f"识别测试完成: 准确率={accuracy:.1%}, 平均相似度={avg_similarity:.3f}")
        
        return result
    
    def _get_adaptive_threshold(self, num_cats: int) -> float:
        """获取自适应阈值"""
        thresholds = {
            5: 0.70,
            10: 0.75,
            20: 0.80,
            30: 0.75,
            50: 0.70
        }
        
        if num_cats in thresholds:
            return thresholds[num_cats]
        
        # 线性插值
        sorted_scales = sorted(thresholds.keys())
        
        if num_cats <= sorted_scales[0]:
            return thresholds[sorted_scales[0]]
        elif num_cats >= sorted_scales[-1]:
            return thresholds[sorted_scales[-1]]
        else:
            for i in range(len(sorted_scales) - 1):
                if sorted_scales[i] <= num_cats <= sorted_scales[i + 1]:
                    x1, y1 = sorted_scales[i], thresholds[sorted_scales[i]]
                    x2, y2 = sorted_scales[i + 1], thresholds[sorted_scales[i + 1]]
                    return y1 + (y2 - y1) * (num_cats - x1) / (x2 - x1)
        
        return 0.75
    
    def compare_models(self, base_model_path: str, continued_model_path: str, 
                      num_cats: int = 20, samples_per_cat: int = 5) -> Dict:
        """对比两个模型的性能"""
        logger.info("开始模型对比测试")
        
        # 测试基础模型
        logger.info("测试基础模型...")
        base_model = self.load_model(base_model_path)
        base_results = self.test_recognition_accuracy(base_model, num_cats, samples_per_cat)
        
        # 测试续训模型
        logger.info("测试续训模型...")
        continued_model = self.load_model(continued_model_path)
        continued_results = self.test_recognition_accuracy(continued_model, num_cats, samples_per_cat)
        
        # 计算改善
        accuracy_improvement = continued_results['accuracy'] - base_results['accuracy']
        similarity_change = continued_results['avg_similarity'] - base_results['avg_similarity']
        
        comparison = {
            'base_model': {
                'path': base_model_path,
                'results': base_results
            },
            'continued_model': {
                'path': continued_model_path,
                'results': continued_results
            },
            'improvement': {
                'accuracy_change': accuracy_improvement,
                'similarity_change': similarity_change,
                'accuracy_improvement_percent': accuracy_improvement * 100
            }
        }
        
        logger.info(f"模型对比完成:")
        logger.info(f"  基础模型准确率: {base_results['accuracy']:.1%}")
        logger.info(f"  续训模型准确率: {continued_results['accuracy']:.1%}")
        logger.info(f"  准确率变化: {accuracy_improvement:+.1%}")
        
        return comparison

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='验证续训模型效果')
    parser.add_argument('--base-model', type=str, default='fast_megadescriptor_100cats_best.pth', 
                       help='基础模型路径')
    parser.add_argument('--continued-model', type=str, default='hard_sample_continued_model.pth', 
                       help='续训模型路径')
    parser.add_argument('--cats', type=int, default=20, help='测试猫咪数量')
    parser.add_argument('--samples', type=int, default=5, help='每只猫咪的注册样本数量')
    parser.add_argument('--output', type=str, default='model_comparison_results.json', help='输出文件')
    
    args = parser.parse_args()
    
    # 检查模型文件
    if not os.path.exists(args.base_model):
        print(f"❌ 基础模型文件不存在: {args.base_model}")
        return
    
    if not os.path.exists(args.continued_model):
        print(f"❌ 续训模型文件不存在: {args.continued_model}")
        return
    
    # 创建验证器
    validator = ModelValidator()
    
    # 对比模型
    results = validator.compare_models(args.base_model, args.continued_model, args.cats, args.samples)
    
    # 保存结果
    with open(args.output, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    # 显示总结
    print("\n" + "="*60)
    print("🔍 模型对比测试结果")
    print("="*60)
    
    base_acc = results['base_model']['results']['accuracy']
    cont_acc = results['continued_model']['results']['accuracy']
    improvement = results['improvement']['accuracy_change']
    
    print(f"\n📊 准确率对比:")
    print(f"   基础模型: {base_acc:.1%}")
    print(f"   续训模型: {cont_acc:.1%}")
    print(f"   改善幅度: {improvement:+.1%}")
    
    if improvement > 0.05:
        print(f"   评价: 🌟 显著改善")
    elif improvement > 0.02:
        print(f"   评价: ✅ 适度改善")
    elif improvement > -0.02:
        print(f"   评价: ⚖️ 性能相当")
    else:
        print(f"   评价: ⚠️ 性能下降")
    
    print(f"\n📁 详细结果已保存: {args.output}")

if __name__ == "__main__":
    main()
